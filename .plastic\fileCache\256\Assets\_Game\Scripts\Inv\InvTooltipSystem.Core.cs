using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Collections.Generic;
using System;
using Inventory;

public partial class InvTooltipSystem : MonoBehaviour
{
    // ------------------- Core Fields -------------------
    [SerializeField] private float hoverDelay = 0.5f;
    [SerializeField] private bool namingEnabled = false; // turn off naming feature for demo

    // UI Toolkit references shared across partials
    private VisualElement root;
    private VisualElement tooltip;
    private TextField nameField;
    private Label detailsLabel;
    private VisualElement contextMenu;

    // Context-action registry (shared)
    private readonly Dictionary<Type, List<(string name, Action<ItemStack, string> action)>> actionRegistry = new();

    // Runtime state
    private string currentSlotType;
    private ItemStack currentStack;
    private Vector2 currentMousePos;
    private bool isTooltipVisible = false;
    private bool isContextVisible = false;
    private Coroutine tooltipCoroutine;

    // External systems
    private FPSPlayerManager playerManager;
    private bool wasInputEnabled = true;
    private InvSlotHandler slotHandler;
    private InvUI invUI;
    private EquipmentManager equipmentManager;

    // Style helpers
    private StyleSheet styleSheet;
    private bool justShowedContextMenu = false;

    // Drag-and-drop safeguard
    private bool isTooltipPrevented = false;

    // ----------------------------------------------------
    // Unity lifecycle
    private void Start()
    {
        Debug.Log("InvTooltipSystem.Start() called");

        equipmentManager = FindObjectOfType<EquipmentManager>();
        invUI            = FindObjectOfType<InvUI>();
        playerManager    = FindObjectOfType<FPSPlayerManager>();
        slotHandler      = new InvSlotHandler(equipmentManager, invUI);

        var dragManager = FindObjectOfType<InvDragAndDropManager>();
        var uiDoc       = dragManager ? dragManager.GetComponent<UIDocument>() : invUI?.GetComponent<UIDocument>();

        if (uiDoc == null)
        {
            Debug.LogError("InvTooltipSystem: Could not find UIDocument - tooltip system will not work!");
            return;
        }

        root = uiDoc.rootVisualElement;
        Debug.Log("InvTooltipSystem: UIDocument found, initializing UI elements");

        // Load stylesheet
        styleSheet = Resources.Load<StyleSheet>("TooltipContextStyles");
        if (styleSheet != null)
        {
            root.styleSheets.Add(styleSheet);
            Debug.Log("InvTooltipSystem: Stylesheet loaded successfully");
        }
        else
        {
            Debug.LogWarning("InvTooltipSystem: Could not load TooltipContextStyles stylesheet");
        }

        // Delegate detailed UI building to other partials
        CreateTooltipUI();
        CreateContextMenuUI();
        RegisterDefaultActions();

        // Global mouse handling (see ContextMenu partial)
        root.RegisterCallback<MouseDownEvent>(OnGlobalMouseDown);

        Debug.Log("InvTooltipSystem: Initialization complete");
    }

    private void Update()
    {
        // When tooltip is open, keep it following the mouse & validate hover state
        if (isTooltipVisible && nameField.focusController.focusedElement != nameField)
        {
            Vector2 mousePosition = Input.mousePosition;
            mousePosition.y = Screen.height - mousePosition.y;

            Vector2 panelPosition = root.panel != null
                ? RuntimePanelUtils.ScreenToPanel(root.panel, mousePosition)
                : mousePosition;

            currentMousePos = panelPosition;
            PositionTooltip(currentMousePos);

            // Ensure we are still hovering the same valid slot
            var elementUnderMouse = root.panel?.Pick(panelPosition);
            bool stillHoveringValidSlot = false;
            VisualElement current = elementUnderMouse;
            while (current != null)
            {
                if (current.userData is string slotType)
                {
                    var stack = slotHandler.GetStackFromSlot(slotType);
                    if (stack != null && stack.Item != null)
                    {
                        if (slotType == currentSlotType)
                        {
                            stillHoveringValidSlot = true;
                            break;
                        }
                        else
                        {
                            HideTooltip();
                            ShowTooltip(slotType, currentMousePos, null);
                            return;
                        }
                    }
                }
                current = current.parent;
            }

            if (!stillHoveringValidSlot)
            {
                HideTooltip();
            }
        }

        // Auto-hide if inventory closed
        if ((isTooltipVisible || isContextVisible) && invUI != null && !invUI.IsInventoryVisible())
        {
            HideTooltip();
            HideContextMenu();
        }
    }

    // ------------------- Input helpers ------------------
    private void DisablePlayerInput()
    {
        if (playerManager != null)
        {
            wasInputEnabled      = playerManager.enabled;
            playerManager.enabled = false;
        }
    }

    private void RestorePlayerInput()
    {
        if (playerManager != null && wasInputEnabled)
        {
            playerManager.enabled = true;
        }
    }

    private void EnsureCursorState()
    {
        if (!isTooltipVisible && !isContextVisible && invUI != null && !invUI.IsInventoryVisible())
        {
            UnityEngine.Cursor.visible   = false;
            UnityEngine.Cursor.lockState = CursorLockMode.Locked;
        }
    }

    // ------------------- Public helpers -----------------
    public void ForceHideTooltip()
    {
        HideTooltip();
        HideContextMenu();
    }

    public void PreventTooltipsWhileDragging()
    {
        HideTooltip();
        HideContextMenu();
        isTooltipPrevented = true;
    }

    public void AllowTooltipsAfterDragging() => isTooltipPrevented = false;

    // ------------------- Persistence --------------------
    private void OnDestroy()            => Item.SavePlayerNames();
    private void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus) Item.SavePlayerNames();
    }
    private void OnApplicationPause(bool pause)
    {
        if (pause) Item.SavePlayerNames();
    }

    // ----------------------------------------------------
    // The following methods are implemented in other partial files:
    //   • CreateTooltipUI / PositionTooltip / ShowTooltip / HideTooltip / etc.
    //   • CreateContextMenuUI / ShowContextMenu / HideContextMenu / etc.
    //   • RegisterDefaultActions / action-registry helpers.
} 