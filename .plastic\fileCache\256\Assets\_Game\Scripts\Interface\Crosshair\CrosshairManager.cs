using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class CrosshairManager : MonoBehaviour
{
    #region Inspector Variables

    [Header("Crosshair Settings")]
    [SerializeField] private Color crosshairColor = Color.white;
    [SerializeField] private float crosshairSize = 4f;
    [SerializeField] private bool screenScaleCompensation = true;
    [SerializeField] private Sprite customCrosshairSprite;
    [SerializeField] private bool useCustomCrosshair = false;

    [Header("Overlay Settings")]
    [SerializeField] private Sprite overlayImageSprite;
    [SerializeField] private Vector2 overlaySize = new Vector2(100f, 100f);
    [SerializeField] private Color overlayColor = Color.white;

    [Header("Dynamic Crosshair")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private float proximityCheckDistance = 1f;
    [SerializeField] private LayerMask proximityLayers = -1;
    [SerializeField] private int requiredHitFrames = 2;
    [SerializeField] private float overlayStabilityThreshold = 0.1f;
    [SerializeField] private bool alwaysShowCrosshair = false;
    
    [Header("Item Detection")]
    [Tooltip("Distance at which to show crosshair for pickup items")]
    [SerializeField] private float itemDetectionDistance = 3f;
    [Tooltip("Show crosshair for items even if overlay is disabled")]
    [SerializeField] private bool showCrosshairForItems = true;
    [Tooltip("How quickly the crosshair fades in when near items")]
    [SerializeField] private float itemCrosshairFadeSpeed = 5f;
    [Tooltip("Maximum opacity when looking directly at items (0-1)")]
    [SerializeField] private float itemCrosshairMaxOpacity = 1f;
    [Tooltip("Opacity when items are nearby but not looked at (0-1)")]
    [SerializeField] private float itemProximityOpacity = 0.3f;
    
    [Header("Item Proximity Detection")]
    [Tooltip("Radius to check for nearby items")]
    [SerializeField] private float itemProximityRadius = 5f;
    [Tooltip("How often to check for nearby items (seconds)")]
    [SerializeField] private float proximityCheckInterval = 0.2f;
    [Tooltip("Enable proximity-based crosshair")]
    [SerializeField] private bool enableProximityDetection = true;

    [Header("Optimization Settings")]
    [Tooltip("How often to update raycasts (in seconds)")]
    [SerializeField] private float raycastUpdateInterval = 0.05f;
    [Tooltip("Minimum camera movement before forcing a raycast update")]
    [SerializeField] private float cameraMovementThreshold = 0.01f;
    [Tooltip("Minimum camera rotation before forcing a raycast update (degrees)")]
    [SerializeField] private float cameraRotationThreshold = 1f;

    #endregion

    #region Private Variables

    private Canvas uiCanvas;
    private Image crosshairDot;
    private Image overlayImage;
    private float baseScreenHeight = 1080f;
    private bool needsTextureUpdate = false;

    // Stability tracking
    private int consecutiveHitFrames = 0;
    private int consecutiveMissFrames = 0;
    private float lastStateChangeTime = 0f;
    private bool currentOverlayState = false;
    private bool forceCrosshairVisible = false;
    
    // Screen interaction tracking
    private bool isLookingAtScreen = false;
    
    // Item detection tracking
    private bool isLookingAtItem = false;
    private bool itemsNearby = false;
    private float currentItemCrosshairOpacity = 0f;
    private float targetItemCrosshairOpacity = 0f;
    
    // Performance optimization
    private float lastRaycastTime = 0f;
    private float lastProximityCheckTime = 0f;
    private Vector3 lastCameraPosition;
    private Quaternion lastCameraRotation;
    
    // Cached data for performance
    private RaycastHit[] raycastHits = new RaycastHit[10]; // Pre-allocated array for performance
    private Collider[] nearbyColliders = new Collider[20]; // Pre-allocated for sphere check
    private Color baseCrosshairColor;

    #endregion

    #region Unity Messages

    private void Awake()
    {
        if (playerCamera == null)
            playerCamera = Camera.main;

        SetupUICanvas();
        SetupCrosshair();
        SetupOverlayImage();
        
        baseCrosshairColor = crosshairColor;
        
        if (!alwaysShowCrosshair && crosshairDot != null)
        {
            crosshairDot.gameObject.SetActive(false);
        }
        
        // Initialize camera tracking
        if (playerCamera != null)
        {
            lastCameraPosition = playerCamera.transform.position;
            lastCameraRotation = playerCamera.transform.rotation;
        }
    }

    private void OnValidate()
    {
        if (crosshairDot != null || overlayImage != null)
        {
            needsTextureUpdate = true;
            UpdateCrosshairAppearance();
            UpdateOverlayAppearance();
            
            if (crosshairDot != null)
            {
                UpdateCrosshairVisibility();
            }
        }
    }

    private void Update()
    {
        // Check for nearby items periodically
        if (enableProximityDetection && Time.time - lastProximityCheckTime >= proximityCheckInterval)
        {
            CheckNearbyItems();
        }
        
        // Update raycasts based on camera movement
        if (ShouldUpdateRaycast())
        {
            PerformCombinedRaycast();
        }
        
        // Always update crosshair fade regardless of raycast timing
        UpdateCrosshairFade();
    }

    private void LateUpdate()
    {
        if (needsTextureUpdate)
        {
            UpdateCrosshairTextures();
            needsTextureUpdate = false;
        }
    }

    #endregion

    #region Setup Methods

    private void SetupUICanvas()
    {
        GameObject canvasObj = new GameObject("UI Canvas");
        uiCanvas = canvasObj.AddComponent<Canvas>();
        uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 1;

        canvasObj.AddComponent<GraphicRaycaster>();
        canvasObj.transform.SetParent(transform);
    }

    private void SetupCrosshair()
    {
        GameObject dotObj = new GameObject("CrosshairDot");
        dotObj.transform.SetParent(uiCanvas.transform);
        crosshairDot = dotObj.AddComponent<Image>();

        UpdateCrosshairTextures();
        UpdateCrosshairAppearance();
        
        crosshairDot.gameObject.SetActive(alwaysShowCrosshair);
    }

    private void SetupOverlayImage()
    {
        GameObject overlayObj = new GameObject("OverlayImage");
        overlayObj.transform.SetParent(uiCanvas.transform);
        overlayImage = overlayObj.AddComponent<Image>();

        UpdateOverlayAppearance();
        overlayImage.gameObject.SetActive(false);
    }

    #endregion

    #region Texture Generation

    private void UpdateCrosshairTextures()
    {
        if (useCustomCrosshair && customCrosshairSprite != null)
        {
            crosshairDot.sprite = customCrosshairSprite;
        }
        else
        {
            crosshairDot.sprite = CreateDiamondSprite();
        }
    }

    private Sprite CreateDiamondSprite()
    {
        int texSize = 64;
        Texture2D tex = new Texture2D(texSize, texSize);
        tex.filterMode = FilterMode.Trilinear;

        float halfSize = texSize * 0.5f;
        Vector2 center = new Vector2(halfSize, halfSize);
        float diamondSize = texSize * 0.15f;

        for (int x = 0; x < texSize; x++)
        {
            for (int y = 0; y < texSize; y++)
            {
                Vector2 pixelPos = new Vector2(x, y) - center;
                float rotX = (pixelPos.x - pixelPos.y) * 0.707107f;
                float rotY = (pixelPos.x + pixelPos.y) * 0.707107f;

                float alpha = (Mathf.Abs(rotX) < diamondSize && Mathf.Abs(rotY) < diamondSize) ? 1 : 0;
                tex.SetPixel(x, y, new Color(1, 1, 1, alpha));
            }
        }

        tex.Apply();
        return Sprite.Create(tex, new Rect(0, 0, texSize, texSize), new Vector2(0.5f, 0.5f));
    }

    #endregion

    #region Update Methods
    
    private bool ShouldUpdateRaycast()
    {
        float timeSinceLastRaycast = Time.time - lastRaycastTime;
        bool timeThresholdMet = timeSinceLastRaycast >= raycastUpdateInterval;
        
        float positionDelta = Vector3.Distance(playerCamera.transform.position, lastCameraPosition);
        bool positionThresholdMet = positionDelta > cameraMovementThreshold;
        
        float rotationDelta = Quaternion.Angle(playerCamera.transform.rotation, lastCameraRotation);
        bool rotationThresholdMet = rotationDelta > cameraRotationThreshold;
        
        return timeThresholdMet || positionThresholdMet || rotationThresholdMet;
    }
    
    private void CheckNearbyItems()
    {
        if (playerCamera == null) return;
        
        lastProximityCheckTime = Time.time;
        
        // Get player layer to exclude
        int playerLayer = LayerMask.NameToLayer("Player");
        LayerMask checkLayers = proximityLayers & ~(1 << playerLayer);
        
        // Perform sphere check
        int hitCount = Physics.OverlapSphereNonAlloc(
            playerCamera.transform.position,
            itemProximityRadius,
            nearbyColliders,
            checkLayers,
            QueryTriggerInteraction.Collide
        );
        
        bool foundItem = false;
        
        // Check each collider for InvItemPickup
        for (int i = 0; i < hitCount; i++)
        {
            if (nearbyColliders[i] == null) continue;
            
            // Skip player-related objects
            if (nearbyColliders[i].CompareTag("Player")) continue;
            
            // Check for item pickup component
            InvItemPickup itemPickup = nearbyColliders[i].GetComponent<InvItemPickup>();
            if (itemPickup == null)
            {
                itemPickup = nearbyColliders[i].GetComponentInParent<InvItemPickup>();
            }
            
            if (itemPickup != null)
            {
                foundItem = true;
                break;
            }
        }
        
        itemsNearby = foundItem;
        UpdateTargetOpacity();
    }
    
    private void PerformCombinedRaycast()
    {
        if (playerCamera == null) return;
        
        // Update tracking
        lastRaycastTime = Time.time;
        lastCameraPosition = playerCamera.transform.position;
        lastCameraRotation = playerCamera.transform.rotation;
        
        // Setup
        int playerLayer = LayerMask.NameToLayer("Player");
        LayerMask modifiedProximityLayers = proximityLayers & ~(1 << playerLayer);
        Vector3 rayOrigin = playerCamera.transform.position + playerCamera.transform.forward * 0.2f;
        
        // Use RaycastAll to get all hits at once - more efficient than multiple raycasts
        float maxDistance = Mathf.Max(proximityCheckDistance, itemDetectionDistance);
        int hitCount = Physics.RaycastNonAlloc(
            rayOrigin,
            playerCamera.transform.forward,
            raycastHits,
            maxDistance,
            modifiedProximityLayers,
            QueryTriggerInteraction.Collide
        );
        
        bool proximityHitDetected = false;
        bool itemHitDetected = false;
        
        // Process all hits in one pass
        for (int i = 0; i < hitCount; i++)
        {
            RaycastHit hit = raycastHits[i];
            
            // Skip player-related hits
            if (hit.collider.CompareTag("Player"))
                continue;
                
            // Check hierarchy for player
            if (hit.distance < 0.3f)
            {
                Transform checkTransform = hit.collider.transform;
                bool isPlayer = false;
                while (checkTransform != null)
                {
                    if (checkTransform.CompareTag("Player"))
                    {
                        isPlayer = true;
                        break;
                    }
                    checkTransform = checkTransform.parent;
                }
                if (isPlayer) continue;
            }
            
            // Check for interactive screen
            if (hit.collider.GetComponent<InteractiveWorldScreen>() != null)
                continue;
                
            // Check if child of self
            if (hit.collider.gameObject.transform.IsChildOf(transform))
                continue;
            
            // Check for proximity hit
            if (!proximityHitDetected && hit.distance <= proximityCheckDistance)
            {
                proximityHitDetected = true;
            }
            
            // Check for item hit
            if (!itemHitDetected && hit.distance <= itemDetectionDistance && showCrosshairForItems)
            {
                InvItemPickup itemPickup = hit.collider.GetComponent<InvItemPickup>();
                if (itemPickup == null)
                {
                    itemPickup = hit.collider.GetComponentInParent<InvItemPickup>();
                }
                
                if (itemPickup != null)
                {
                    itemHitDetected = true;
                }
            }
            
            // Early exit if we found everything
            if (proximityHitDetected && itemHitDetected)
                break;
        }
        
        // Update proximity state
        UpdateProximityState(proximityHitDetected);
        
        // Update item detection state
        UpdateItemState(itemHitDetected);
    }
    
    private void UpdateProximityState(bool hitDetected)
    {
        if (isLookingAtScreen)
        {
            consecutiveHitFrames = 0;
            consecutiveMissFrames = requiredHitFrames;
            
            if (currentOverlayState)
            {
                currentOverlayState = false;
                lastStateChangeTime = Time.time;
                overlayImage.gameObject.SetActive(false);
                UpdateCrosshairVisibility();
            }
            return;
        }
        
        if (hitDetected)
        {
            consecutiveHitFrames++;
            consecutiveMissFrames = 0;
        }
        else
        {
            consecutiveMissFrames++;
            consecutiveHitFrames = 0;
        }

        bool shouldBeVisible = currentOverlayState;
        if (consecutiveHitFrames >= requiredHitFrames)
        {
            shouldBeVisible = true;
        }
        else if (consecutiveMissFrames >= requiredHitFrames)
        {
            shouldBeVisible = false;
        }

        if (shouldBeVisible != currentOverlayState &&
            Time.time - lastStateChangeTime >= overlayStabilityThreshold)
        {
            currentOverlayState = shouldBeVisible;
            lastStateChangeTime = Time.time;
            
            if (overlayImage != null)
            {
                overlayImage.gameObject.SetActive(currentOverlayState);
            }
            
            UpdateCrosshairVisibility();
        }
    }
    
    private void UpdateItemState(bool itemDetected)
    {
        isLookingAtItem = itemDetected;
        UpdateTargetOpacity();
    }
    
    private void UpdateTargetOpacity()
    {
        // Determine target opacity based on state
        if (isLookingAtItem)
        {
            // Looking directly at item - full opacity
            targetItemCrosshairOpacity = itemCrosshairMaxOpacity;
        }
        else if (itemsNearby && enableProximityDetection)
        {
            // Items nearby but not looking at them - proximity opacity
            targetItemCrosshairOpacity = itemProximityOpacity;
        }
        else
        {
            // No items around
            targetItemCrosshairOpacity = 0f;
        }
        
        UpdateCrosshairVisibility();
    }
    
    private void UpdateCrosshairFade()
    {
        if (crosshairDot == null) return;
        
        // Smoothly fade the opacity
        currentItemCrosshairOpacity = Mathf.Lerp(
            currentItemCrosshairOpacity, 
            targetItemCrosshairOpacity, 
            Time.deltaTime * itemCrosshairFadeSpeed
        );
        
        // Apply the opacity to the crosshair color
        if (showCrosshairForItems && currentItemCrosshairOpacity > 0.01f)
        {
            Color currentColor = baseCrosshairColor;
            currentColor.a = baseCrosshairColor.a * currentItemCrosshairOpacity;
            crosshairDot.color = currentColor;
        }
        else if (forceCrosshairVisible || alwaysShowCrosshair || currentOverlayState)
        {
            // Reset to full opacity for other visibility reasons
            crosshairDot.color = baseCrosshairColor;
        }
    }

    private void UpdateCrosshairAppearance()
    {
        if (crosshairDot == null) return;

        float scaleFactor = screenScaleCompensation ? Screen.height / baseScreenHeight : 1f;
        float scaledSize = crosshairSize * scaleFactor;

        baseCrosshairColor = crosshairColor;
        crosshairDot.color = crosshairColor;

        if (useCustomCrosshair && customCrosshairSprite != null)
        {
            crosshairDot.rectTransform.sizeDelta = new Vector2(crosshairSize, crosshairSize);
            crosshairDot.preserveAspect = true;
        }
        else
        {
            crosshairDot.rectTransform.sizeDelta = new Vector2(scaledSize, scaledSize);
        }

        SetRectTransformToCenter(crosshairDot.rectTransform);
    }

    private void SetRectTransformToCenter(RectTransform rectTransform)
    {
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);
        rectTransform.anchoredPosition = Vector2.zero;
    }

    private void UpdateOverlayAppearance()
    {
        if (overlayImage == null || overlayImageSprite == null) return;

        overlayImage.sprite = overlayImageSprite;

        Rect spriteRect = overlayImageSprite.rect;
        float aspectRatio = spriteRect.width / spriteRect.height;
        float width = overlaySize.x;
        float height = overlaySize.y;

        if (width / height > aspectRatio)
        {
            width = height * aspectRatio;
        }
        else
        {
            height = width / aspectRatio;
        }

        overlayImage.color = overlayColor;
        overlayImage.rectTransform.sizeDelta = new Vector2(width, height);

        SetRectTransformToCenter(overlayImage.rectTransform);
    }
    
    private void UpdateCrosshairVisibility()
    {
        if (crosshairDot != null)
        {
            // Show crosshair if:
            // - Not looking at screen AND
            // - (Force visible OR always show OR overlay state OR item opacity > 0)
            bool shouldShow = !isLookingAtScreen && 
                             (forceCrosshairVisible || 
                              alwaysShowCrosshair || 
                              currentOverlayState || 
                              (showCrosshairForItems && currentItemCrosshairOpacity > 0.01f));
            crosshairDot.gameObject.SetActive(shouldShow);
        }
    }

    #endregion

    #region Public Methods

    public void SetCrosshairType(bool useCustom)
    {
        useCustomCrosshair = useCustom;
        UpdateCrosshairTextures();
        UpdateCrosshairAppearance();
    }

    public void SetCustomCrosshairSprite(Sprite newSprite)
    {
        customCrosshairSprite = newSprite;
        if (useCustomCrosshair)
        {
            UpdateCrosshairTextures();
            UpdateCrosshairAppearance();
        }
    }
    
    public void SetCrosshairOpacityExternal(float newOpacity)
    {
        forceCrosshairVisible = newOpacity > 0.01f;
        UpdateCrosshairVisibility();
    }
    
    // Called by InteractiveWorldScreen when player looks at/away from screen
    public void SetScreenLookState(bool lookingAtScreen)
    {
        isLookingAtScreen = lookingAtScreen;
        UpdateCrosshairVisibility();
    }

    #endregion

    #if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (playerCamera != null)
        {
            Vector3 rayOrigin = playerCamera.transform.position + playerCamera.transform.forward * 0.2f;
            
            // Draw combined ray showing max distance
            float maxDistance = Mathf.Max(proximityCheckDistance, itemDetectionDistance);
            Gizmos.color = isLookingAtItem ? Color.cyan : (currentOverlayState ? Color.green : Color.yellow);
            Gizmos.DrawRay(rayOrigin, playerCamera.transform.forward * maxDistance);
            
            // Draw distance markers
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(rayOrigin + playerCamera.transform.forward * proximityCheckDistance, 0.05f);
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(rayOrigin + playerCamera.transform.forward * itemDetectionDistance, 0.05f);
            
            // Draw proximity sphere
            if (enableProximityDetection)
            {
                Gizmos.color = itemsNearby ? new Color(0, 1, 1, 0.2f) : new Color(0, 0, 1, 0.1f);
                Gizmos.DrawWireSphere(playerCamera.transform.position, itemProximityRadius);
            }
        }
    }
    #endif
}