using UnityEngine;
using KinematicCharacterController;
using KinematicCharacterController.FPS;
using System.Collections;

public class KinematicClimbingSystem : MonoBehaviour
{
    [Head<PERSON>("Wall Detection")]
    [Tooltip("How far to check for climbable surfaces")]
    public float climbDetectionDistance = 0.8f;

    [Tooltip("Width of climb detection box cast")]
    public float climbDetectionWidth = 0.3f;

    [Tooltip("Maximum angle from vertical to consider wall climbable")]
    public float maxClimbAngle = 30f;

    [Tooltip("Layers that can be climbed")]
    public LayerMask climbableLayers;

    [Header("Movement")]
    [Tooltip("How quickly to snap to the climbing position")]
    public float climbSnapSpeed = 15f;

    [Tooltip("Distance to maintain from wall while climbing")]
    public float wallOffset = 0.3f;

    [Header("Idle Slide")]
    [Tooltip("Speed at which you slowly slide down when not pressing up")]
    public float idleSlideSpeed = 1f;

    [Head<PERSON>("Wall Jump Settings")]
    [Tooltip("Upward force for 'vertical' jump")]
    public float wallJumpUpForce = 12f;

    [<PERSON>lt<PERSON>("Outward/away force for side jump")]
    public float wallJumpAwayForce = 15f;

    [<PERSON>lt<PERSON>("Angle below which the player will let go (downward look)")]
    public float lookDownLetGoAngle = 35f;

    [Tooltip("Angle above which the player will jump mostly up")]
    public float lookUpJumpAngle = 30f;

    [Header("Climbing Controls")]
    [Tooltip("If true, allows both toggle and hold to climb")]
    public bool allowToggleClimbing = true;

    [Tooltip("How long to wait before allowing another toggle")]
    public float toggleCooldown = 0.2f;

    #region Private Fields
    private bool isClimbingToggled = false;
    private float lastToggleTime = 0f;
    private bool wasMouseButtonPressed = false;

    private KinematicCharacterMotor motor;
    private FPSCharacterController characterController;
    private PlayerStatus playerStatus;
    private Transform cameraTransform;

    private bool isClimbing = false;
    private Vector3 climbNormal;
    private Vector3 climbPoint;
    private bool canGrabNewSurface = true;
    private float _postClimbJumpLockoutTimer = 0f;

    [Header("Detection")]
    public float wallCheckDistance = 1f;
    public float minJumpHeight = 1.2f;
    [Tooltip("Time required after leaving ground before wall running is allowed")]
    public float groundToWallDelay = 0.1f;
    private float timeLeftGround;

    #endregion
    #region Unity Methods

    private void Start()
    {
        InitializeComponents();
        
        // Disable climbing system by default
        // It will be enabled when the climbing axe is equipped
        enabled = false;
        
        // Ensure the ClimbingSystemActivator is present
        if (GetComponent<ClimbingSystemActivator>() == null && GetComponentInParent<ClimbingSystemActivator>() == null)
        {
            gameObject.AddComponent<ClimbingSystemActivator>();
            Debug.Log("Added ClimbingSystemActivator to manage climbing axe functionality");
        }
        
        Debug.Log("KinematicClimbingSystem initialized but disabled - equip a climbing axe to enable");
    }

    private void Update()
    {
        // Only run climbing if in default or climbing states
        if (characterController.CurrentCharacterState != CharacterState.Default &&
            characterController.CurrentCharacterState != CharacterState.Climbing)
        {
            return;
        }

        if (isClimbing)
        {
            UpdateClimbing();
        }
        else if (ShouldAttemptClimbing())
        {
            TryStartClimbing();
        }
    }
    #endregion

    #region Initialization
    private void InitializeComponents()
    {
        motor = GetComponent<KinematicCharacterMotor>();
        characterController = GetComponent<FPSCharacterController>();
        playerStatus = GetComponent<PlayerStatus>();

        // Attempt to find the camera transform
        if (characterController != null && characterController.CameraFollowPoint != null)
        {
            cameraTransform = characterController.CameraFollowPoint;
        }
        else
        {
            var charCamera = FindObjectOfType<FPSCharacterCamera>();
            if (charCamera != null)
            {
                cameraTransform = charCamera.transform;
            }
        }

        if (!motor || !characterController || !playerStatus || !cameraTransform)
        {
            Debug.LogError("KinematicClimbingSystem: Missing required components!");
            enabled = false;
        }
    }
    #endregion

    #region Climbing Entry
    private bool ShouldAttemptClimbing()
    {
        // Ensure we consider jump lockouts
        if (_postClimbJumpLockoutTimer > 0)
        {
            return false;
        }

        bool isMouseDown = Input.GetMouseButton(0);

        // Handle toggling of climbing
        if (isMouseDown && !wasMouseButtonPressed)
        {
            wasMouseButtonPressed = true;
            if (!isClimbing && allowToggleClimbing && Time.time - lastToggleTime > toggleCooldown)
            {
                isClimbingToggled = !isClimbingToggled;
                lastToggleTime = Time.time;
            }
        }
        else if (!isMouseDown)
        {
            wasMouseButtonPressed = false;
        }

        // Only climb if: (mouse down or toggled), not on ground, can grab, has energy
        bool shouldClimb = (isMouseDown || isClimbingToggled)
                           && !motor.GroundingStatus.IsStableOnGround
                           && canGrabNewSurface
                           && playerStatus.currentEnergy > 0;

        // If we land on ground, disable toggled climbing
        if (motor.GroundingStatus.IsStableOnGround)
        {
            isClimbingToggled = false;
        }

        return shouldClimb;
    }

    private void TryStartClimbing()
    {
        Vector3 origin = motor.TransientPosition + Vector3.up * (motor.Capsule.height * 0.6f);
        Vector3 direction = GetClimbDetectionDirection();

        if (DetectClimbableSurface(origin, direction, out RaycastHit wallHit))
        {
            StartClimbing(wallHit.point, wallHit.normal);
        }
    }

    private Vector3 GetClimbDetectionDirection()
    {
        Vector3 direction = cameraTransform.forward;
        direction.y = 0f; // only horizontal
        return direction.normalized;
    }

    private bool DetectClimbableSurface(Vector3 origin, Vector3 direction, out RaycastHit wallHit)
    {
        Vector3 boxHalfExtents = new Vector3(climbDetectionWidth * 0.5f, 0.1f, 0.1f);
        Quaternion boxRotation = Quaternion.LookRotation(direction);

        Debug.DrawRay(origin, direction * climbDetectionDistance, Color.red);

        if (Physics.BoxCast(origin, boxHalfExtents, direction, out wallHit,
            boxRotation, climbDetectionDistance, climbableLayers))
        {
            // Check if near vertical
            Vector3 adjustedNormal = wallHit.normal;
            float horizontalMagnitude = new Vector2(adjustedNormal.x, adjustedNormal.z).magnitude;

            // Example tweak to handle angled walls
            if (horizontalMagnitude > 0.8f)
            {
                adjustedNormal = new Vector3(wallHit.normal.x * 0.5f, 0f, wallHit.normal.z * 0.5f).normalized;
            }

            float surfaceAngle = Vector3.Angle(adjustedNormal, Vector3.up);
            // If steeper than 90 - maxClimbAngle => valid for climbing
            if (surfaceAngle > (90f - maxClimbAngle))
            {
                wallHit.normal = adjustedNormal;
                return true;
            }
        }
        return false;
    }

    private void StartClimbing(Vector3 point, Vector3 normal)
    {
        isClimbing = true;
        climbPoint = point;
        climbNormal = normal;

        Vector3 targetPos = GetClimbingPosition();
        Vector3 safePos = SweepToPosition(motor.TransientPosition, targetPos);

        motor.SetMovementCollisionsSolvingActivation(true);
        motor.SetGroundSolvingActivation(false);

        motor.SetTransientPosition(safePos);
        motor.BaseVelocity = Vector3.zero;

        characterController.TransitionToState(CharacterState.Climbing);

        // Optionally keep sprinting logic going for stamina drain
        playerStatus.SetSprintingAttempt(true);

        Debug.Log($"[Climbing] Started climbing at point {point:F3}, normal {normal:F3}, snapped to {safePos:F3}");
    }
    #endregion

    #region Climbing Update
    private void UpdateClimbing()
    {
        bool shouldStopClimbing = false;

        // If we ran out of energy, we could decide to force a drop:
        if (playerStatus.currentEnergy <= 0)
        {
            // ...
        }
        // If let go of mouse or toggled off
        else if (!Input.GetMouseButton(0) && !isClimbingToggled)
        {
            shouldStopClimbing = true;
        }
        // If toggled again
        else if (isClimbingToggled && Input.GetMouseButtonDown(0) && Time.time - lastToggleTime > toggleCooldown)
        {
            shouldStopClimbing = true;
        }

        // Or if the surface is no longer valid
        if (shouldStopClimbing || !ValidateClimbingSurface())
        {
            StopClimbing();
            return;
        }

        // If spacebar => do wall jump
        if (Input.GetKeyDown(KeyCode.Space))
        {
            PerformWallJump();
            return;
        }

        HandleClimbingMovement();
    }

    private void HandleClimbingMovement()
    {
        Vector3 currentPos = motor.TransientPosition;
        Vector3 targetPos = GetClimbingPosition();

        // small force to stick to wall
        Vector3 wallStickForce = -climbNormal * 2f;

        // Lerp to cling position
        Vector3 newPos = Vector3.Lerp(
            currentPos,
            targetPos,
            1f - Mathf.Exp(-climbSnapSpeed * Time.deltaTime)
        );

        // Turn that into a velocity
        Vector3 velocity = (newPos - currentPos) / Time.deltaTime;
        velocity += wallStickForce;

        motor.BaseVelocity = velocity;
    }

    private Vector3 GetClimbingPosition()
    {
        // Position so that capsule is offset from the wall by "wallOffset"
        return climbPoint
               - climbNormal * wallOffset
               - Vector3.up * (motor.Capsule.height * 0.5f);
    }

    private bool ValidateClimbingSurface()
    {
        Vector3 checkOrigin = motor.TransientPosition + Vector3.up * (motor.Capsule.height * 0.5f);

        // Check a few angles around climbNormal to ensure there's still a wall
        bool hitAny = false;
        for (float angle = -30f; angle <= 30f; angle += 30f)
        {
            Vector3 checkDirection = Quaternion.Euler(0, angle, 0) * -climbNormal;
            if (Physics.Raycast(checkOrigin, checkDirection,
                out RaycastHit hit, climbDetectionDistance + wallOffset, climbableLayers))
            {
                hitAny = true;
                break;
            }
        }
        return hitAny;
    }

    #endregion
    #region Wall Jump
    private void PerformWallJump()
    {
        Vector3 lookDir = cameraTransform.forward.normalized;

        float pitchAngle = CalculatePitchAngle(lookDir);
        float angleToWall = Vector3.Angle(lookDir, -climbNormal);

        Vector3 jumpVelocity;

        if (pitchAngle < -lookDownLetGoAngle)
        {
            jumpVelocity = climbNormal * 2f + Vector3.down * 3f;
            Debug.Log("Wall Jump: Dropping (looking down)");
        }
        else if (angleToWall < 45f)
        {
            jumpVelocity = Vector3.up * wallJumpUpForce + climbNormal * wallJumpAwayForce * 0.5f;
            Debug.Log("Wall Jump: Upward jump (looking at wall)");
        }
        else
        {
            Vector3 directionalJump = (lookDir + climbNormal * 0.3f + Vector3.up * 0.5f).normalized;
            jumpVelocity = directionalJump * wallJumpAwayForce;
            jumpVelocity += Vector3.up * wallJumpUpForce * 0.5f;

            Debug.Log($"Wall Jump: Directional\n" +
                      $"Pitch Angle: {pitchAngle:F1}°\n" +
                      $"Angle to Wall: {angleToWall:F1}°\n" +
                      $"Jump direction: {directionalJump}");
        }

        motor.ForceUnground(0.2f);
        motor.BaseVelocity = jumpVelocity;
        // Tell the wall run system we just left the ground
        var wallRunSystem = GetComponent<KinematicWallRun>();
        if (wallRunSystem != null)
        {
            // Reset the ground timer to prevent immediate wall run detection
            wallRunSystem.ResetGroundTimer();
        }

        characterController.SetPostClimbJumpLockout(0.1f);
        StopClimbing();
    }

    private float CalculatePitchAngle(Vector3 forward)
    {
        Vector3 horizontalForward = new Vector3(forward.x, 0f, forward.z).normalized;
        return Vector3.SignedAngle(horizontalForward, forward, cameraTransform.right);
    }

    #endregion
    #region Exit Climb

    private void StopClimbing()
    {
        if (!isClimbing) return;

        isClimbing = false;
        isClimbingToggled = false;

        // Re-enable ground logic
        motor.SetGroundSolvingActivation(true);

        // Cancel "fake sprint" if you want
        playerStatus.SetSprintingAttempt(false);

        // <<-- This calls the method in FPSCharacterController.cs
        characterController.SetPostClimbJumpLockout(0.2f);

        characterController.TransitionToState(CharacterState.Default);
        StartCoroutine(ClimbCooldown());

        Debug.Log("[Climbing] Stopped climbing");
    }

    public void SetPostClimbJumpLockout(float seconds)
    {
        _postClimbJumpLockoutTimer = seconds;
    }

    private IEnumerator ClimbCooldown()
    {
        canGrabNewSurface = false;
        yield return new WaitForSeconds(0.5f);
        canGrabNewSurface = true;
    }
    #endregion

    #region Capsule Sweep
    private Vector3 SweepToPosition(Vector3 startPos, Vector3 endPos)
    {
        float distance = Vector3.Distance(startPos, endPos);
        if (distance < 0.001f)
            return endPos;

        Vector3 direction = (endPos - startPos).normalized;
        float radius = motor.Capsule.radius;
        float height = motor.Capsule.height;

        Vector3 bottom = startPos + Vector3.up * radius;
        Vector3 top = bottom + Vector3.up * (height - 2f * radius);

        if (Physics.CapsuleCast(bottom, top, radius, direction, out RaycastHit hit, distance, motor.CollidableLayers))
        {
            float safeDist = Mathf.Max(0f, hit.distance - 0.01f);
            return startPos + direction * safeDist;
        }
        return endPos;
    }
    #endregion

    #region GUI and Debug
    private void OnGUI()
    {
        if (isClimbingToggled)
        {
            GUI.Label(new Rect(Screen.width / 2 - 60, Screen.height - 40, 120, 30), "Climbing Toggled");
        }
    }
#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (!Application.isPlaying || !cameraTransform || !motor) return;

        // Only draw debug info if we're actually climbing
        if (isClimbing)
        {
            // Character center
            Vector3 characterCenter = motor.transform.position + Vector3.up * (motor.Capsule.height * 0.5f);

            // Full look direction
            Vector3 lookDirection = cameraTransform.forward.normalized;
            // Horizontal/flat look direction
            Vector3 flatLookDirection = new Vector3(lookDirection.x, 0f, lookDirection.z).normalized;

            // 1) Draw the climbing point
            Gizmos.color = Color.magenta;
            Gizmos.DrawSphere(climbPoint, 0.1f);

            // 2) Draw the wall normal from the character center
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(characterCenter, climbNormal * 2f);

            // 3) Draw camera's forward (red), offset slightly to +X
            Gizmos.color = Color.red;
            Gizmos.DrawRay(characterCenter + Vector3.right * 0.05f, lookDirection * 3f);

            // 4) Draw horizontal direction (green), offset slightly to -X
            Gizmos.color = Color.green;
            Gizmos.DrawRay(characterCenter - Vector3.right * 0.05f, flatLookDirection * 3f);

            // 5) Draw the angles on screen
            float pitchAngle = CalculatePitchAngle(lookDirection);
            Vector3 screenPoint = UnityEditor.HandleUtility.WorldToGUIPoint(characterCenter + Vector3.up * 1.5f);
            UnityEditor.Handles.BeginGUI();
            GUI.color = Color.yellow;
            GUI.Label(new Rect(screenPoint.x, screenPoint.y, 300, 50),
                $"<b>Climbing Debug</b>\n" +
                $"Pitch: {pitchAngle:F1}°");
            UnityEditor.Handles.EndGUI();
        }
    }
#endif



    #endregion
}