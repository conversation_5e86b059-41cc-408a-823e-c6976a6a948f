using UnityEngine;

public class Item : ScriptableObject
{
    [HideInInspector] public string itemName; // Original name - hidden from player
    public string Description;
    public int Price;
    public Sprite Icon;
    public string IconResourcePath => $"ItemIcons/{name}";
    
    [Tooltip("3D model prefab to use when this item is dropped in the world")]
    public GameObject WorldModelPrefab;
    
    [SerializeField] private Dimensions slotDimension = new Dimensions(1, 1);
    public Dimensions SlotDimension => slotDimension;

    public int maxStack;
    public bool isHorizontal = true;
    public float Weight;

    // Player-given name system
    [System.NonSerialized] private string playerGivenName = "";
    private static System.Collections.Generic.Dictionary<string, string> playerNameDatabase = new System.Collections.Generic.Dictionary<string, string>();

    // Property to get/set player name
    public string PlayerGivenName
    {
        get 
        { 
            // Try to get from database first
            if (playerNameDatabase.TryGetValue(GetItemID(), out string savedName))
            {
                playerGivenName = savedName;
                return savedName;
            }
            return playerGivenName;
        }
        set 
        { 
            playerGivenName = value;
            // Save to database
            playerNameDatabase[GetItemID()] = value;
        }
    }

    // Check if player has named this item
    public bool HasPlayerName => !string.IsNullOrEmpty(PlayerGivenName);

    // Get unique ID for this item type
    public string GetItemID()
    {
        return $"{name}_{itemName}";
    }

    // Helper property to quickly check if item is larger than 1x1
    public bool IsMultiSlot => slotDimension.Width > 1 || slotDimension.Height > 1;

    // Ensure item name is always set
    private void OnValidate()
    {
        if (string.IsNullOrEmpty(itemName))
        {
            itemName = name;
            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }
    }

    // Called when the scriptable object is created
    private void OnEnable()
    {
        if (string.IsNullOrEmpty(itemName))
        {
            itemName = name;
        }
    }

    // Save/Load player names
    public static void SavePlayerNames()
    {
        string json = JsonUtility.ToJson(new SerializableDictionary<string, string>(playerNameDatabase));
        PlayerPrefs.SetString("ItemPlayerNames", json);
        PlayerPrefs.Save();
    }

    public static void LoadPlayerNames()
    {
        if (PlayerPrefs.HasKey("ItemPlayerNames"))
        {
            string json = PlayerPrefs.GetString("ItemPlayerNames");
            var loaded = JsonUtility.FromJson<SerializableDictionary<string, string>>(json);
            if (loaded != null)
            {
                playerNameDatabase = loaded.ToDictionary();
            }
        }
    }
}

[System.Serializable]
public class Dimensions
{
    [SerializeField] private int width = 1;
    [SerializeField] private int height = 1;

    public int Width => width;
    public int Height => height;

    public Dimensions(int width = 1, int height = 1)
    {
        this.width = Mathf.Max(1, width);
        this.height = Mathf.Max(1, height);
    }
}

public interface IEquippable
{
    void Equip(EquipmentManager equipmentManager);
}

public enum EquipmentSlotType
{
    HeadSlot,
    ChestSlot,
    BagSlot,
}

public abstract class EquipmentBase : Item, IEquippable
{
    [HideInInspector]
    public EquipmentSlotType Slot;
    public float WeightCapacityBonus;

    public abstract void Equip(EquipmentManager equipmentManager);
}

public enum BoonType
{
    // Define your boon types here
    None,
    HealthBoost,
    DamageBoost,
    SpeedBoost
    // Add more as needed
}

[System.Serializable]
public class SerializableDictionary<TKey, TValue>
{
    public System.Collections.Generic.List<TKey> keys = new System.Collections.Generic.List<TKey>();
    public System.Collections.Generic.List<TValue> values = new System.Collections.Generic.List<TValue>();

    public SerializableDictionary(System.Collections.Generic.Dictionary<TKey, TValue> dict)
    {
        foreach (var kvp in dict)
        {
            keys.Add(kvp.Key);
            values.Add(kvp.Value);
        }
    }

    public System.Collections.Generic.Dictionary<TKey, TValue> ToDictionary()
    {
        var dict = new System.Collections.Generic.Dictionary<TKey, TValue>();
        for (int i = 0; i < keys.Count && i < values.Count; i++)
        {
            dict[keys[i]] = values[i];
        }
        return dict;
    }
}