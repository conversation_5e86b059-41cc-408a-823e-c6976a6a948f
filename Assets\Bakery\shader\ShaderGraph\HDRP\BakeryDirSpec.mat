%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: BakeryDirSpec
  m_Shader: {fileID: -6465566751694194690, guid: ce4d1cf3f6b53504aaeccb1de008d85e, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _DISABLE_SSR_TRANSPARENT
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2225
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  - MOTIONVECTORS
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - RayTracingPrepass
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - Texture2D_90EBC0DE:
        m_Texture: {fileID: 2800000, guid: f7cb3bf80f9eec2429456177b478a2ed, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_985C6C2F:
        m_Texture: {fileID: 2800000, guid: f5a4f568ff21d5249a4eeb6c1b8ac488, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_9ABD554F:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_9EED425C:
        m_Texture: {fileID: 2800000, guid: f7cb3bf80f9eec2429456177b478a2ed, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_C0C9873F:
        m_Texture: {fileID: 2800000, guid: f5a4f568ff21d5249a4eeb6c1b8ac488, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Texture2D_D6D4A158:
        m_Texture: {fileID: 2800000, guid: f7cb3bf80f9eec2429456177b478a2ed, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BakeryDirSpec_BFF7753_Texture2D99DD35BE_2986012004:
        m_Texture: {fileID: 2800000, guid: f5a4f568ff21d5249a4eeb6c1b8ac488, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2DAsset_4EB92580_Out_0:
        m_Texture: {fileID: 2800000, guid: f5a4f568ff21d5249a4eeb6c1b8ac488, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2DAsset_6385D840_Out_0:
        m_Texture: {fileID: 2800000, guid: f7cb3bf80f9eec2429456177b478a2ed, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2DAsset_F14C9B5B_Out_0:
        m_Texture: {fileID: 2800000, guid: f5a4f568ff21d5249a4eeb6c1b8ac488, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_1555B618: 0.1
    - Vector1_29AA64CF: 0.492
    - Vector1_C3FA7048: 0.5
    - _AddPrecomputedVelocity: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _BlendMode: 0
    - _ConservativeDepthOffsetEnable: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DepthOffsetEnable: 0
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _Metallic: 0
    - _OpaqueCullMode: 2
    - _RayTracing: 0
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RefractionModel: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Smoothness: 0
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &6081559522214058086
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 13
  hdPluginSubTargetMaterialVersions:
    m_Keys: []
    m_Values: 
