using UnityEngine;
using System.Collections.Generic;

[CreateAssetMenu(fileName = "New Manual", menuName = "Items/Manual")]
public class Manual : ConsumableDefinition
{
    [System.Serializable]
    public class ItemInformation
    {
        [Tooltip("The item name to match (exact match required)")]
        public string targetItemName;
        
        [Tooltip("The name to apply to the item (leave empty to skip)")]
        public string itemNameToApply;
        
        [Tooltip("The description to apply to the item (leave empty to skip)")]
        [TextArea(3, 5)]
        public string itemDescriptionToApply;
    }
    
    [Header("Manual Information")]
    [Tooltip("List of items this manual provides information about")]
    public List<ItemInformation> itemInformationList = new List<ItemInformation>();
    
    [Tooltip("If true, shows a notification for each item learned")]
    public bool showIndividualNotifications = true;
    
    [Tooltip("Message shown when the manual is used")]
    public string useMessage = "You study the manual carefully...";
    
    [Tooltip("Message shown after learning (use {0} for count of items learned)")]
    public string completionMessage = "You learned about {0} item(s)!";
    
    public override void Use(PlayerStatus playerStatus)
    {
        if (playerStatus == null) return;
        
        // Show initial message
        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(useMessage))
        {
            NotificationManager.Instance.ShowNotification(useMessage);
        }
        
        int itemsLearned = 0;
        
        // Apply information to each listed item
        foreach (var info in itemInformationList)
        {
            if (string.IsNullOrEmpty(info.targetItemName)) continue;
            
            // Find the item in the database
            Item targetItem = ItemDatabase.GetItemByName(info.targetItemName);
            if (targetItem == null)
            {
                Debug.LogWarning($"Manual: Could not find item '{info.targetItemName}' in database");
                continue;
            }
            
            bool learned = false;
            
            // Apply name if provided
            if (!string.IsNullOrEmpty(info.itemNameToApply))
            {
                targetItem.PlayerGivenName = info.itemNameToApply;
                learned = true;
            }
            
            // Apply description if provided
            if (!string.IsNullOrEmpty(info.itemDescriptionToApply))
            {
                targetItem.PlayerGivenDescription = info.itemDescriptionToApply;
                learned = true;
            }
            
            if (learned)
            {
                itemsLearned++;
                
                // Show individual notification if enabled
                if (showIndividualNotifications && NotificationManager.Instance != null)
                {
                    string learnedName = !string.IsNullOrEmpty(info.itemNameToApply) 
                        ? info.itemNameToApply 
                        : targetItem.itemName;
                    NotificationManager.Instance.ShowNotification($"Learned about: {learnedName}");
                }
            }
        }
        
        // Save the player names/descriptions
        Item.SavePlayerNames();
        
        // Show completion message
        if (NotificationManager.Instance != null && !string.IsNullOrEmpty(completionMessage))
        {
            NotificationManager.Instance.ShowNotification(string.Format(completionMessage, itemsLearned));
        }
        
        // Base consumable functionality (restore energy/health if set)
        base.Use(playerStatus);
    }
}

// Extension to ConsumableDefinition to support virtual Use method
public partial class ConsumableDefinition : Item
{
    public virtual void Use(PlayerStatus playerStatus)
    {
        if (playerStatus != null)
        {
            if (EnergyRestore > 0)
            {
                playerStatus.UpdateEnergy(playerStatus.currentEnergy + EnergyRestore);
            }
            if (HealthRestore > 0)
            {
                playerStatus.RestoreHealth(HealthRestore);
            }
        }
    }
}