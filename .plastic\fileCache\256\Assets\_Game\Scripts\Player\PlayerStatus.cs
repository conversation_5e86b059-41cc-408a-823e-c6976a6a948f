using UnityEngine;
using System;
using System.Collections;
using KinematicCharacterController.FPS;

public class PlayerStatus : MonoBehaviour
{
    #region Variables
    // Add a debug logging flag
    private static bool verboseLogging = false;

    private void Log(string message)
    {
        if (verboseLogging || Debug.isDebugBuild)
        {
            Debug.Log(message);
        }
    }

    private EquipmentManager equipmentManager;
    private InvUI inventoryUI;
    private FPSCharacterController characterController;

    public event Action<float, float> OnHealthChanged;
    public event Action<float, float> OnEnergyChanged;
    public event Action OnSprintingStarted;
    public event Action OnSprintingStopped;
    public event Action OnWeightChanged;
    public event Action<int> OnCurrencyChanged;
    public event Action<int, int> OnHitsChanged;

    [Header("Hit System")]
    [SerializeField] private int maxHits = 3; // Maximum number of hits player can take
    [SerializeField] private int currentHits; // Current number of hits taken
    [SerializeField] private int baseMaxHits = 3; // Base maximum hits without armor
    private int armorBonusHits = 0; // Additional hits from armor

    [Header("Energy")]
    public float maxEnergy = 1000f;
    public float currentEnergy;
    [SerializeField] private float energyDrainRate = 20f; // Energy drained per second when sprinting

    [Header("Sprinting")]
    private bool isSprinting = false;
    private bool sprintingAttempted = false;

    [Header("Weight")]
    public float weightSpeedReductionFactor = 0.01f;
    public float currentWeight;
    public float baseWeight = 50f;
    [SerializeField] private float totalWeightCapacityBonus = 0f;
    public float effectiveWeightCapacity;

    // Flag to track if the application is quitting
    private bool isQuitting = false;

    [Header("Currency")]
    private CurrencyManager currencyManager;

    [Header("Debuffs")]
    public float maxSpeedReduction = 0.7f;

    [Header("Invulnerability")]
    [SerializeField] private float invulnerabilityDuration = 0.2f;
    private bool isInvulnerable = false;
    private Coroutine invulnerabilityCoroutine;

    #endregion

    #region Properties
    public float EnergyPercentage => currentEnergy / maxEnergy;
    public bool IsSprinting => isSprinting;
    public int MaxHits => maxHits;
    public int CurrentHits => currentHits;
    public int Currency
    {
        get
        {
            // Always use CurrencyManager
            if (currencyManager != null)
            {
                return currencyManager.CurrentCurrency;
            }
            return 0; // Should never happen if properly initialized
        }
    }
    #endregion

    #region Initialization
    private void Awake()
    {
        currentEnergy = maxEnergy;
        currentHits = 0; // Start with 0 hits taken
        maxHits = baseMaxHits; // Initialize max hits to base value

        // Don't initialize currency to 0 here, as it will be loaded from PlayerProgressionManager
        Log($"PlayerStatus Awake: Initial currency: {Currency}");
    }

    private void Start()
    {
        InitializeComponents();
        UpdateWeight();
        UpdateWeightCapacityBonus();

        // Find CurrencyManager for currency operations
        currencyManager = CurrencyManager.Instance;
        if (currencyManager == null)
        {
            Debug.LogError("[PlayerStatus] CurrencyManager not found! Spawning a new one.");
            // Create a GameObject and add CurrencyManager to ensure it exists
            GameObject cmObj = new GameObject("CurrencyManager");
            currencyManager = cmObj.AddComponent<CurrencyManager>();
        }

        // Subscribe to currency change events
        currencyManager.OnCurrencyChanged += HandleCurrencyChanged;

        // Force an immediate update to ensure UI is in sync
        OnCurrencyChanged?.Invoke(currencyManager.CurrentCurrency);

        Log($"PlayerStatus Start: Currency: {Currency}");

        // Start a delayed check to verify currency was properly initialized
        StartCoroutine(VerifyCurrencyAfterDelay());
    }

    private void InitializeComponents()
    {
        equipmentManager = GetComponent<EquipmentManager>();
        inventoryUI = FindObjectOfType<InvUI>();
        characterController = GetComponent<FPSCharacterController>();

        if (characterController == null)
        {
            Debug.LogError("FPSCharacterController component not found on the player!");
        }
    }

    private IEnumerator VerifyCurrencyAfterDelay()
    {
        // Wait to give PlayerProgressionManager time to set currency
        yield return new WaitForSeconds(0.5f);

        // Force-update currency display if needed
        if (CurrencyManager.Instance != null)
        {
            CurrencyManager.Instance.NotifyAllListeners();
        }

        // If currency is still 0, try to recover from backup sources
        if (Currency == 0)
        {
            Log("PlayerStatus: Currency still 0 after delay, checking backup sources");

            // Check player_currency_last
            if (PlayerPrefs.HasKey("player_currency_last"))
            {
                int backupCurrency = PlayerPrefs.GetInt("player_currency_last", 0);
                if (backupCurrency > 0)
                {
                    Log($"PlayerStatus: Found backup currency value: {backupCurrency}");
                    ForceSetCurrency(backupCurrency);
                }
            }

            // Also check emergency backup
            if (PlayerPrefs.HasKey("emergency_currency_backup"))
            {
                int emergencyCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
                if (emergencyCurrency > Currency)
                {
                    Log($"PlayerStatus: Found emergency currency: {emergencyCurrency}");
                    ForceSetCurrency(emergencyCurrency);
                }
            }

            // Finally ask PlayerProgressionManager directly
            var progressManager = PlayerProgressionManager.Instance;
            if (progressManager != null && progressManager.PlayerMoney > Currency)
            {
                Log($"PlayerStatus: Using currency from progression manager: {progressManager.PlayerMoney}");
                ForceSetCurrency(progressManager.PlayerMoney);
            }

            // Make sure CurrencyManager notifies all listeners of the final value
            yield return null;
            if (currencyManager != null)
            {
                currencyManager.NotifyAllListeners();
            }
        }
    }
    #endregion

    #region Update
    private void Update()
    {
        // Handle energy for sprinting
        if (sprintingAttempted)
        {
            if (currentEnergy > 0f)
            {
                DrainEnergy();
            }
            else
            {
                if (isSprinting)
                {
                    StopSprinting();
                }
            }
        }

        UpdateMovementDebuff();
    }
    #endregion

    #region Energy & Sprinting Management
    public void SetSprintingAttempt(bool isAttempting)
    {
        if (sprintingAttempted != isAttempting)
        {
            sprintingAttempted = isAttempting;

            if (!sprintingAttempted && isSprinting)
            {
                StopSprinting();
            }
            else if (sprintingAttempted && currentEnergy > 0f && !isSprinting)
            {
                StartSprinting();
            }
        }
    }

    private void DrainEnergy()
    {
        float energyToReduce = energyDrainRate * Time.deltaTime;
        float oldEnergy = currentEnergy;
        currentEnergy -= energyToReduce;
        currentEnergy = Mathf.Clamp(currentEnergy, 0f, maxEnergy);

        // Notify energy change
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);

        if (currentEnergy <= 0f)
        {
            StopSprinting();
        }
    }

    private void StartSprinting()
    {
        isSprinting = true;
        OnSprintingStarted?.Invoke();
    }

    private void StopSprinting()
    {
        isSprinting = false;
        OnSprintingStopped?.Invoke();
    }
    #endregion

    #region Damage & Death
    public void TakeDamage(int damage)
    {
        // Note: Fall damage is now handled by FallDamageSystem component
        if (isInvulnerable)
            return;

        // Convert damage to a hit
        int hitsToAdd = Mathf.Max(1, damage / 30); // Example: 30+ damage = 1 hit, 60+ damage = 2 hits, etc.
        
        // Add hit(s)
        int oldHits = currentHits;
        currentHits += hitsToAdd;
        
        // Notify UI of "health" change for compatibility
        OnHealthChanged?.Invoke(maxHits - currentHits, maxHits);
        
        Log($"Player took {hitsToAdd} hit(s). Total hits: {currentHits}/{maxHits}");
        
        // Notify hit change
        OnHitsChanged?.Invoke(oldHits, currentHits);

        if (currentHits >= maxHits)
        {
            Die();
        }
        else
        {
            if (invulnerabilityCoroutine != null)
            {
                StopCoroutine(invulnerabilityCoroutine);
            }
            invulnerabilityCoroutine = StartCoroutine(InvulnerabilityRoutine());
        }
    }

    private void Die()
    {
        Log("Player has died!");

        // Notify the death manager
        DeathManager deathManager = DeathManager.Instance;
        if (deathManager != null)
        {
            deathManager.HandlePlayerDeath();
        }
        else
        {
            Debug.LogError("DeathManager not found! Player died but couldn't process death sequence.");

            // Emergency health restore if no death manager found
            currentHits = 0;
            OnHealthChanged?.Invoke(maxHits, maxHits);
        }
    }

    private IEnumerator InvulnerabilityRoutine()
    {
        isInvulnerable = true;
        yield return new WaitForSeconds(invulnerabilityDuration);
        isInvulnerable = false;
    }
    #endregion

    #region Weight Management
    public void UpdateWeight()
    {
        float totalWeight = 0f;

        if (equipmentManager != null)
        {
            foreach (var slot in equipmentManager.GetEquipmentSlots())
            {
                if (slot.equippedItem != null)
                {
                    totalWeight += slot.equippedItem.Weight;

                    // Add weight of items in storage containers
                    if (slot.storageContainer != null)
                    {
                        foreach (var stack in slot.storageContainer.GetItems())
                        {
                            if (stack != null && stack.Item != null)
                            {
                                totalWeight += stack.Item.Weight;
                            }
                        }
                    }
                }
            }
        }

        currentWeight = totalWeight;
    }

    public void UpdateWeightCapacityBonus()
    {
        float totalBonus = 0f;
        foreach (var slot in equipmentManager.GetEquipmentSlots())
        {
            if (slot.equippedItem != null)
            {
                totalBonus += slot.equippedItem.WeightCapacityBonus;
            }
        }
        effectiveWeightCapacity = baseWeight + totalBonus;
        OnWeightChanged?.Invoke();
    }

    private void UpdateMovementDebuff()
    {
        float speedReduction = 0f;

        if (currentWeight > effectiveWeightCapacity)
        {
            float excessWeight = currentWeight - effectiveWeightCapacity;
            speedReduction = excessWeight * weightSpeedReductionFactor;
        }

        if (currentEnergy <= 0)
        {
            speedReduction += 0.5f;
        }

        speedReduction = Mathf.Clamp(speedReduction, 0f, maxSpeedReduction);

        // Apply speed debuff based on weight and energy
        // This applies based on your current movement speed
        if (characterController != null)
        {
            // The FPS character controller doesn't have a direct SpeedDebuff method,
            // but we can calculate what percentage of max speed the character should move at
            float speedMultiplier = 1f - speedReduction;

            // You would need to implement a method in FPSCharacterController to handle this speed reduction
            // For now, we'll leave this commented out as it depends on your implementation
            // characterController.SetSpeedMultiplier(speedMultiplier);
        }
    }
    #endregion

    #region Health & Energy Management
    public void UpdateEnergy(float newEnergy)
    {
        float oldEnergy = currentEnergy;
        currentEnergy = Mathf.Clamp(newEnergy, 0, maxEnergy);
        OnEnergyChanged?.Invoke(oldEnergy, currentEnergy);
    }

    public void UpdateCurrency(int newCurrency)
    {
        // Always use currency manager
        currencyManager.SetCurrency(newCurrency);
        // Don't need to do anything else - our HandleCurrencyChanged will be called
    }

    public void AddCurrency(int amount)
    {
        // Always use currency manager
        currencyManager.AddCurrency(amount);
    }

    public bool TrySpendCurrency(int amount)
    {
        // Always use currency manager
        return currencyManager.TrySpendCurrency(amount);
    }

    // Method to reset hits upon respawn
    public void ResetHits()
    {
        int oldHits = currentHits;
        currentHits = 0;
        OnHitsChanged?.Invoke(oldHits, currentHits);
        OnHealthChanged?.Invoke(maxHits, maxHits); // Notify UI of full "health"
    }

    // Method to update max hits based on armor
    public void UpdateMaxHits(int armorBonus)
    {
        armorBonusHits = armorBonus;
        int oldMaxHits = maxHits;
        maxHits = baseMaxHits + armorBonusHits;
        
        // Ensure current hits doesn't exceed new max
        if (currentHits > maxHits)
        {
            int oldHits = currentHits;
            currentHits = maxHits;
            OnHitsChanged?.Invoke(oldHits, currentHits);
        }
        
        // Update health display
        float healthPercentage = 1f - ((float)currentHits / maxHits);
        OnHealthChanged?.Invoke(healthPercentage * maxHits, maxHits);
        
        Log($"Max hits updated from {oldMaxHits} to {maxHits} (base: {baseMaxHits}, armor bonus: {armorBonusHits})");
    }

    // Method to be called when armor is equipped/unequipped
    public void RecalculateArmorHits()
    {
        int totalArmorHits = 0;
        
        // Loop through all equipped armor items
        if (equipmentManager != null)
        {
            foreach (var slot in equipmentManager.GetEquipmentSlots())
            {
                if (slot.equippedItem != null && slot.equippedItem is Armor armor)
                {
                    totalArmorHits += armor.ExtraHitPoints;
                }
            }
        }
        
        // Update max hits with the calculated total
        UpdateMaxHits(totalArmorHits);
    }

    // Modify the RestoreHealth method to work with hits system
    public void RestoreHealth(float amount)
    {
        // Convert health restore to hit reduction
        int hitsToRemove = Mathf.FloorToInt((amount / maxHits));
        if (hitsToRemove > 0)
        {
            int oldHits = currentHits;
            currentHits = Mathf.Max(0, currentHits - hitsToRemove);
            OnHitsChanged?.Invoke(oldHits, currentHits);
            OnHealthChanged?.Invoke(maxHits - currentHits, maxHits);
            Log($"Restored {hitsToRemove} hit(s). Remaining hits: {currentHits}/{maxHits}");
        }
    }

    public void RestoreEnergy(float amount)
    {
        UpdateEnergy(currentEnergy + amount);
    }

    // Method to fully restore energy (for use with special items)
    public void FullyRestoreEnergy()
    {
        UpdateEnergy(maxEnergy);
    }
    #endregion

    #region Utility
    private void OnValidate()
    {
        effectiveWeightCapacity = baseWeight + totalWeightCapacityBonus;
    }

    // Simplified method to set currency
    public void ForceSetCurrency(int newValue)
    {
        // Always use currency manager
        currencyManager.SetCurrency(newValue);
        Log($"[PlayerStatus] Force-set currency to: {newValue}");
    }

    // Save currency when application quits
    private void OnApplicationQuit()
    {
        // Set flag that we're quitting
        isQuitting = true;

        // Explicitly save our currency to all backup sources
        if (currencyManager != null)
        {
            // Force the currency manager to save
            int currentCurrency = currencyManager.CurrentCurrency;

            // Create backup in PlayerPrefs for redundancy
            PlayerPrefs.SetInt("emergency_currency_backup", currentCurrency);
            PlayerPrefs.SetInt("player_currency_last", currentCurrency);
            PlayerPrefs.Save();

            Log($"[PlayerStatus] Created emergency currency backup on quit: {currentCurrency}");

            // Also try to save through progression manager
            var progressManager = PlayerProgressionManager.Instance;
            if (progressManager != null)
            {
                progressManager.PlayerMoney = currentCurrency;
                progressManager.SavePlayerCurrency();
                Log($"[PlayerStatus] Saved currency to PlayerProgressionManager: {currentCurrency}");
            }
        }
    }

    // Add extra save on destroy to catch scene transitions
    private void OnDestroy()
    {
        // Unsubscribe from currency manager events
        if (currencyManager != null)
        {
            currencyManager.OnCurrencyChanged -= HandleCurrencyChanged;
        }
    }

    // Handle currency changes from CurrencyManager
    private void HandleCurrencyChanged(int oldValue, int newValue)
    {
        // Just forward the event to our subscribers
        OnCurrencyChanged?.Invoke(newValue);
    }
    #endregion
}