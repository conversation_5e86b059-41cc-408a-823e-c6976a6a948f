using UnityEngine;
using KinematicCharacterController.FPS; // Add this for CharacterState

public class ViewModelHeadBob : MonoBehaviour
{
    [Head<PERSON>("References")]
    [Tooltip("Reference to the parent HeadBob component")]
    public HeadBob parentHeadBob;

    [<PERSON><PERSON>("Bob Intensity")]
    [Tooltip("Multiplier for position bobbing (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bobPositionMultiplier = 0.5f;

    [Toolt<PERSON>("Multiplier for rotation bobbing (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bobRotationMultiplier = 0.5f;

    [Tooltip("Multiplier for landing bounce effects (0-1 where 1 is full parent intensity)")]
    [Range(0f, 1f)]
    public float bounceMultiplier = 0.5f;

    [Head<PERSON>("Camera Motion")]
    [Tooltip("How quickly the viewmodel follows camera motion")]
    [Range(1f, 20f)]
    public float cameraFollowSpeed = 5f;

    [Tooltip("Maximum position offset for camera motion (how far the viewmodel can move)")]
    public Vector3 maxPositionOffset = new Vector3(0.1f, 0.1f, 0.05f);

    [Tooltip("Maximum rotation offset for camera motion (in degrees)")]
    public Vector3 maxRotationOffset = new Vector3(5f, 10f, 5f);

    [Tooltip("Deadzone for camera motion (minimum movement before viewmodel follows)")]
    [Range(0f, 1f)]
    public float cameraDeadzone = 0.05f;

    [Header("Side Sway")]
    [Tooltip("Enable side-sway effect when turning")]
    public bool enableSideSway = true;
    
    [Tooltip("How much the viewmodel moves horizontally when turning (higher = more movement)")]
    [Range(0f, 2f)]
    public float sideSwayAmount = 0.8f;
    
    [Tooltip("How quickly the viewmodel catches up with camera rotation (lower = more lag)")]
    [Range(0.1f, 10f)]
    public float sideSwayLerpSpeed = 3f;
    
    [Tooltip("Maximum side-sway distance in local space units")]
    public float maxSideSwayDistance = 0.1f;

    [Header("Terminal Velocity Settings")]
    [Tooltip("Maximum shake intensity during terminal velocity falls")]
    [Range(0f, 1f)]
    public float maxTerminalShakeMultiplier = 0.1f;

    [Tooltip("Speed at which terminal velocity shake is reduced")]
    public float terminalShakeDamping = 10f;
    
    [Header("Landing Effect Tuning")]
    [Tooltip("Vertical multiplier for landing bounce (stronger = bigger drop)")]
    [Range(0.5f, 2f)]
    public float landingVerticalMultiplier = 1.2f;
    
    [Tooltip("Horizontal multiplier for erratic landing effects")]
    [Range(0f, 1f)]
    public float erraticHorizontalMultiplier = 0.3f;

    [Header("Wall Collision Prevention")]
    [Tooltip("Should wall collision prevention be enabled")]
    public bool enableWallCollisionPrevention = true;
    
    [Tooltip("Enable debug visualization")]
    public bool showDebugVisuals = false;
    
    [Tooltip("How often to check for wall collisions (in seconds)")]
    public float wallCheckInterval = 0.02f; // 50 times per second by default
    
    [Tooltip("Layers that are considered walls/obstacles")]
    public LayerMask wallLayers;
    
    [Tooltip("Distance to check for walls")]
    public float wallCheckDistance = 0.3f;
    
    [Tooltip("How quickly the viewmodel moves away from walls")]
    public float wallAvoidanceSpeed = 8f;
    
    [Tooltip("Maximum push-back distance when colliding with walls")]
    public float maxWallPushDistance = 0.15f;
    
    [Tooltip("Rotation applied when pushing away from walls (in degrees)")]
    public Vector3 wallCollisionRotation = new Vector3(5f, -10f, 5f);
    
    [Tooltip("Points to check for wall collisions (local to viewmodel)")]
    public Vector3[] collisionCheckPoints;

    [Header("Slide Visual Effects")]
    [Tooltip("Additional rotation applied to the viewmodel during slides (in degrees)")]
    public Vector3 slideViewModelRotation = new Vector3(0f, 0f, 45f); // 45 degrees around Z axis for inward tilt

    [Tooltip("Additional position offset during slides (local space)")]
    public Vector3 slideViewModelOffset = new Vector3(0.1f, -0.05f, 0f); // Slightly right and down

    [Tooltip("How quickly the slide effects are applied")]
    [Range(1f, 20f)]
    public float slideViewModelSpeed = 10f;

    // Private variables
    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;
    private Vector3 lastCameraEulerAngles;
    private Vector3 currentCameraVelocity;
    private Vector3 smoothedCameraVelocity;
    private float cameraSmoothingFactor = 0.8f;
    
    // Side-sway variables
    private float targetSideSwayOffset = 0f;
    private float currentSideSwayOffset = 0f;
    private float lastCameraYaw = 0f;
    private Quaternion lastCameraRotationQuat;
    private Vector3 sideSwayVelocity;
    
    // Additional state tracking
    private bool isFlying = false;
    private float effectiveShakeMultiplier = 0f;

    // Random values for erratic bounces
    private Vector3 randomBounceDirection;
    
    // Wall collision variables
    private Vector3 wallAvoidanceOffset = Vector3.zero;
    private Quaternion wallAvoidanceRotation = Quaternion.identity;
    private bool isCollidingWithWall = false;

    // Cached transform references
    private Transform cachedTransform;
    private Transform cachedCameraTransform;
    
    // Raycast optimization
    private RaycastHit[] raycastHits;
    private float nextWallCheckTime;
    private const int MAX_RAYCAST_HITS = 8; // Maximum number of hits to process

    private Quaternion currentSlideViewModelRotation = Quaternion.identity;
    private Vector3 currentSlideViewModelPosition = Vector3.zero;
    private bool wasSliding = false;

    private void Start()
    {
        if (parentHeadBob == null)
        {
            // Try to find the HeadBob component in parent objects
            parentHeadBob = GetComponentInParent<HeadBob>();
            if (parentHeadBob == null)
            {
                // If not found in parents, try to find it in the scene
                parentHeadBob = FindObjectOfType<HeadBob>();
                if (parentHeadBob == null)
                {
                    Debug.LogError("ViewModelHeadBob: No HeadBob component found in the scene. Please assign one manually.");
                    enabled = false;
                    return;
                }
            }
        }

        // Store initial transform values
        initialLocalPosition = transform.localPosition;
        initialLocalRotation = transform.localRotation;

        // Initialize camera tracking
        if (parentHeadBob.playerCamera != null)
        {
            lastCameraRotationQuat = parentHeadBob.playerCamera.transform.rotation;
            lastCameraEulerAngles = parentHeadBob.playerCamera.transform.eulerAngles;
            lastCameraYaw = lastCameraEulerAngles.y;
        }
        else
        {
            Debug.LogWarning("ViewModelHeadBob: No camera reference found in parent HeadBob.");
        }
        
        // Initialize random bounce direction
        RegenerateRandomBounceDirection();
        
        // Initialize collision check points if not set
        if (collisionCheckPoints == null || collisionCheckPoints.Length == 0)
        {
            // Default points to check (front of weapon, barrel, etc.)
            collisionCheckPoints = new Vector3[]
            {
                new Vector3(0, 0, 0.2f),   // Front center
                new Vector3(0.05f, 0, 0.15f),  // Front right
                new Vector3(-0.05f, 0, 0.15f), // Front left
                new Vector3(0, 0.05f, 0.15f)   // Front top
            };
        }
        
        // Set default wall layers if not assigned
        if (wallLayers.value == 0)
        {
            // Typically use the Default layer (index 0) and ignore player layers
            wallLayers = Physics.DefaultRaycastLayers & ~(1 << LayerMask.NameToLayer("Player"));
        }
        
        // Cache transform references
        cachedTransform = transform;
        if (parentHeadBob?.playerCamera != null)
        {
            cachedCameraTransform = parentHeadBob.playerCamera.transform;
        }
        
        // Initialize raycast hit array
        raycastHits = new RaycastHit[MAX_RAYCAST_HITS];
        nextWallCheckTime = 0f;
    }

    private void Update()
    {
        // Check if in fly mode/noclip
        isFlying = DebugFlyController.IsFlying;
        
        // Update effective shake multiplier
        if (isFlying)
        {
            // When flying, quickly reduce shake to zero
            effectiveShakeMultiplier = Mathf.Lerp(effectiveShakeMultiplier, 0f, Time.deltaTime * terminalShakeDamping);
        }
        else
        {
            // When not flying, gradually approach our configured multiplier
            effectiveShakeMultiplier = Mathf.Lerp(effectiveShakeMultiplier, maxTerminalShakeMultiplier, Time.deltaTime * 2f);
        }
    }

    private void LateUpdate()
    {
        if (parentHeadBob == null)
            return;

        ApplyHeadBobEffects();

        if (parentHeadBob.playerCamera != null)
        {
            ApplyCameraMotionOffset();
            
            if (enableWallCollisionPrevention && !isFlying)
            {
                CheckWallCollision();
            }
            else
            {
                // Reset wall avoidance when disabled or flying
                wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, Vector3.zero, Time.deltaTime * wallAvoidanceSpeed);
                wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, Quaternion.identity, Time.deltaTime * wallAvoidanceSpeed);
                isCollidingWithWall = false;
            }
        }
    }

    private void ApplyHeadBobEffects()
    {
        // Base offsets
        Vector3 bobOffset = Vector3.zero;
        Quaternion bobRotation = Quaternion.identity;
        Vector3 momentumOffset = Vector3.zero;
        Vector3 impactOffset = Vector3.zero;
        Vector3 shakeOffset = Vector3.zero;

        // Check if player is sliding
        bool isSliding = false;
        if (parentHeadBob.fpsController != null)
        {
            isSliding = parentHeadBob.fpsController.CurrentCharacterState == CharacterState.Sliding;
        }

        // When not in fly mode, apply all effects
        if (!isFlying)
        {
            // Get parent bob values with reduced intensity
            bobOffset = parentHeadBob.DebugCurrentBobOffset * bobPositionMultiplier;

            // For rotation, use Slerp between identity and the parent bob rotation
            bobRotation = Quaternion.Slerp(
                Quaternion.identity,
                parentHeadBob.DebugCurrentBobRotation,
                bobRotationMultiplier
            );

            // Apply momentum with the same multiplier
            momentumOffset = parentHeadBob.DebugMomentumOffset * bobPositionMultiplier;

            // Handle landing bounce - now with support for variable duration and erratic bounces
            if (parentHeadBob.DebugIsLandingBounce)
            {
                // Get force and normalize timer against actual duration
                float bounceAmount = parentHeadBob.DebugLandingBounceForce * bounceMultiplier;
                
                // Check if we need to regenerate our random bounce values
                if (parentHeadBob.DebugLandingBounceTimer < 0.05f)
                {
                    RegenerateRandomBounceDirection();
                }
                
                // Calculate normalized progress (0-1)
                float duration = parentHeadBob.DebugLandingBounceDuration;
                float t = Mathf.Clamp01(parentHeadBob.DebugLandingBounceTimer / duration);
                
                if (!parentHeadBob.DebugLandingBounceErratic)
                {
                    // Smooth bounce: a dampened sine wave for natural motion
                    float damping = 5f;
                    float bounce = bounceAmount * Mathf.Sin(t * Mathf.PI) * Mathf.Exp(-t * damping);
                    
                    // Make viewmodel drop slightly more than camera for visual impact
                    impactOffset = Vector3.down * bounce * landingVerticalMultiplier;
                }
                else
                {
                    // Erratic bounce: use random direction for more chaotic motion
                    float shakeDecay = 1f - t;
                    float shakeIntensity = bounceAmount * shakeDecay;
                    
                    // Apply perlin noise for erratic motion
                    float noiseTime = Time.time * parentHeadBob.DebugLandingBounceFrequency * 5f;
                    Vector3 erraticMotion = new Vector3(
                        (Mathf.PerlinNoise(noiseTime, 0f) * 2f - 1f) * erraticHorizontalMultiplier,
                        (Mathf.PerlinNoise(0f, noiseTime) * 2f - 1f),
                        (Mathf.PerlinNoise(noiseTime, noiseTime) * 2f - 1f) * erraticHorizontalMultiplier * 0.5f
                    );
                    
                    // Mix in our random stable direction for consistency
                    Vector3 stableMotion = randomBounceDirection * (1f - t * 2f);
                    Vector3 combinedMotion = Vector3.Lerp(stableMotion, erraticMotion, t * 0.7f);
                    
                    // Apply vertical multiplier for stronger downward motion
                    combinedMotion.y *= landingVerticalMultiplier;
                    
                    impactOffset = combinedMotion * shakeIntensity;
                }
            }
        }

        // Apply terminal velocity shake with dynamically adjusted intensity
        if (parentHeadBob.DebugTerminalVelocityShakeIntensity > 0)
        {
            // Use our dynamic multiplier that's reduced when flying
            float shakeIntensity = parentHeadBob.DebugTerminalVelocityShakeIntensity * effectiveShakeMultiplier;
            
            // Only apply shake if intensity is meaningful
            if (shakeIntensity > 0.001f)
            {
                float noiseTime = Time.time * 40f; // Match frequency used in HeadBob

                // Reduced magnitude and only apply to X and Y
                shakeOffset = new Vector3(
                    (Mathf.PerlinNoise(noiseTime, 0f) * 2f - 1f) * 0.5f,
                    (Mathf.PerlinNoise(0f, noiseTime) * 2f - 1f) * 0.5f,
                    0f
                ) * shakeIntensity;
            }
        }

        // Apply slide effects
        Vector3 slideTiltOffset = parentHeadBob.DebugSlideTiltRotation * bobRotationMultiplier;
        Quaternion slideTilt = Quaternion.Euler(slideTiltOffset);
        
        // Apply our custom viewmodel slide rotation and position
        if (isSliding)
        {
            // Target rotation is our defined slide viewmodel rotation
            Quaternion targetSlideViewModelRotation = Quaternion.Euler(slideViewModelRotation);
            
            // Smoothly transition to the slide tilt
            currentSlideViewModelRotation = Quaternion.Slerp(
                currentSlideViewModelRotation, 
                targetSlideViewModelRotation, 
                Time.deltaTime * slideViewModelSpeed
            );
            
            // Smoothly transition to the slide position offset
            currentSlideViewModelPosition = Vector3.Lerp(
                currentSlideViewModelPosition,
                slideViewModelOffset,
                Time.deltaTime * slideViewModelSpeed
            );
            
            wasSliding = true;
        }
        else if (wasSliding)
        {
            // Return to normal when not sliding
            currentSlideViewModelRotation = Quaternion.Slerp(
                currentSlideViewModelRotation, 
                Quaternion.identity, 
                Time.deltaTime * slideViewModelSpeed
            );
            
            // Return position to normal
            currentSlideViewModelPosition = Vector3.Lerp(
                currentSlideViewModelPosition,
                Vector3.zero,
                Time.deltaTime * slideViewModelSpeed
            );
            
            // Check if we're very close to identity, then reset completely
            if (Quaternion.Angle(currentSlideViewModelRotation, Quaternion.identity) < 0.1f 
                && currentSlideViewModelPosition.magnitude < 0.01f)
            {
                currentSlideViewModelRotation = Quaternion.identity;
                currentSlideViewModelPosition = Vector3.zero;
                wasSliding = false;
            }
        }
        
        // Apply side-sway effect - now using position offset instead of rotation
        Vector3 sideSwayOffset = Vector3.zero;
        if (enableSideSway)
        {
            sideSwayOffset = Vector3.right * currentSideSwayOffset;
        }
        
        // Apply wall collision avoidance
        Vector3 finalPosition = initialLocalPosition + bobOffset + momentumOffset + impactOffset + shakeOffset + sideSwayOffset + wallAvoidanceOffset + currentSlideViewModelPosition;
        
        // Add our viewmodel slide rotation to the final rotation
        Quaternion finalRotation = initialLocalRotation * bobRotation * slideTilt * currentSlideViewModelRotation * wallAvoidanceRotation;
        
        // Set final position and rotation
        transform.localPosition = finalPosition;
        transform.localRotation = finalRotation;
    }
    
    private void RegenerateRandomBounceDirection()
    {
        // Generate a stable random direction for this bounce instance
        // This helps create more consistent bounce direction per landing
        randomBounceDirection = new Vector3(
            Random.Range(-1f, 1f) * erraticHorizontalMultiplier,
            Random.Range(-1f, 0f), // More downward bias
            Random.Range(-1f, 1f) * erraticHorizontalMultiplier * 0.5f
        ).normalized;
    }

    private void ApplyCameraMotionOffset()
    {
        // Skip camera motion when sliding to prevent jitter
        if (parentHeadBob.fpsController != null && parentHeadBob.fpsController.CurrentCharacterState == CharacterState.Sliding)
            return;
        Transform cameraTransform = parentHeadBob.playerCamera.transform;

        // Calculate camera rotation change since last frame
        Vector3 currentRotation = cameraTransform.eulerAngles;
        Vector3 rotationDelta = NormalizeEulerAngleDelta(currentRotation - lastCameraEulerAngles);

        // Calculate smooth camera velocity for this frame
        currentCameraVelocity = rotationDelta / Time.deltaTime;
        
        // Apply much stronger smoothing when flying to reduce erratic movements
        float actualSmoothingFactor = isFlying ? 0.95f : cameraSmoothingFactor;
        smoothedCameraVelocity = Vector3.Lerp(smoothedCameraVelocity, currentCameraVelocity, 1f - actualSmoothingFactor);

        // Clamp velocity to reasonable values to prevent violent motions
        smoothedCameraVelocity = Vector3.ClampMagnitude(smoothedCameraVelocity, 300f);

        // Apply deadzone - ignore very small movements
        Vector3 normalizedVelocity = Vector3.zero;
        if (smoothedCameraVelocity.magnitude > cameraDeadzone)
        {
            // Scale velocity differently when flying
            float velocityScaleFactor = isFlying ? 240f : 120f;
            normalizedVelocity = smoothedCameraVelocity / velocityScaleFactor;
        }

        // Calculate offsets based on camera motion
        Vector3 positionOffset = new Vector3(
            -normalizedVelocity.y * maxPositionOffset.x,
            normalizedVelocity.x * maxPositionOffset.y,
            0f
        );

        Vector3 rotationOffset = new Vector3(
            -normalizedVelocity.y * maxRotationOffset.x,
            normalizedVelocity.x * maxRotationOffset.y,
            -normalizedVelocity.x * maxRotationOffset.z
        );

        // Apply stronger dampening when flying
        float effectiveFollowSpeed = isFlying ? cameraFollowSpeed * 0.5f : cameraFollowSpeed;

        // Apply the offsets with smooth interpolation
        Vector3 targetPosition = initialLocalPosition + positionOffset;
        transform.localPosition = Vector3.Lerp(transform.localPosition, targetPosition, Time.deltaTime * effectiveFollowSpeed);

        // Create a temporary rotation offset and apply it with smooth interpolation
        Quaternion targetRotation = initialLocalRotation * Quaternion.Euler(rotationOffset);
        transform.localRotation = Quaternion.Slerp(transform.localRotation, targetRotation, Time.deltaTime * effectiveFollowSpeed);

        // Apply side-sway effect based on camera yaw (left/right turning)
        if (enableSideSway && !isFlying)
        {
            // Get current camera rotation
            Quaternion currentCameraRotation = cameraTransform.rotation;
            
            // Calculate how much the camera has rotated since last frame
            float currentYaw = currentRotation.y;
            
            // Calculate yaw difference (how much we turned left/right)
            float yawDelta = NormalizeAngle(currentYaw - lastCameraYaw);
            
            // Calculate immediate sway effect from turning
            float swayImpulse = -yawDelta * sideSwayAmount * 0.05f;
            
            // Directly apply the impulse to current offset (no target accumulation)
            currentSideSwayOffset += swayImpulse;
            
            // Clamp to maximum distance
            currentSideSwayOffset = Mathf.Clamp(currentSideSwayOffset, -maxSideSwayDistance, maxSideSwayDistance);
            
            // Always apply spring force back to center
            // This happens regardless of whether we're turning or not
            float returnStrength = 8f; // Spring strength - higher means faster return
            float returnForce = -currentSideSwayOffset * returnStrength * Time.deltaTime;
            
            // Apply the return force
            currentSideSwayOffset += returnForce;
            
            // Remember current yaw for next frame
            lastCameraYaw = currentYaw;
            lastCameraRotationQuat = currentCameraRotation;
        }
        else
        {
            // When disabled or flying, immediately reset sway
            // Much stronger return force when disabled
            float returnStrength = 15f;
            currentSideSwayOffset += -currentSideSwayOffset * returnStrength * Time.deltaTime;
            
            // If very close to zero, just snap to prevent tiny residual values
            if (Mathf.Abs(currentSideSwayOffset) < 0.001f)
            {
                currentSideSwayOffset = 0f;
            }
            
            lastCameraYaw = currentRotation.y;
            lastCameraRotationQuat = cameraTransform.rotation;
        }

        // Remember this frame's camera rotation for the next frame
        lastCameraEulerAngles = currentRotation;
    }
    
    private void CheckWallCollision()
    {
        // Only check walls at the specified interval
        if (Time.time < nextWallCheckTime)
        {
            return;
        }
        nextWallCheckTime = Time.time + wallCheckInterval;

        bool hitWall = false;
        float closestDistance = wallCheckDistance;
        Vector3 closestHitPoint = Vector3.zero;
        Vector3 closestHitNormal = Vector3.forward;
        
        // Get camera position and forward direction
        Vector3 cameraPosition = cachedCameraTransform.position;
        Vector3 cameraForward = cachedCameraTransform.forward;
        
        // Check multiple points on the viewmodel for potential wall collisions
        foreach (Vector3 localCheckPoint in collisionCheckPoints)
        {
            // Convert local point to world space
            Vector3 worldCheckPoint = cachedTransform.TransformPoint(localCheckPoint);
            
            if (showDebugVisuals)
            {
                Debug.DrawLine(cameraPosition, worldCheckPoint, Color.yellow, wallCheckInterval);
            }
            
            // Cast ray from camera to check point
            Vector3 checkDirection = worldCheckPoint - cameraPosition;
            float checkDistance = checkDirection.magnitude;
            
            // Use non-alloc raycast to avoid garbage
            int hitCount = Physics.RaycastNonAlloc(cameraPosition, checkDirection.normalized, 
                raycastHits, checkDistance + wallCheckDistance, wallLayers);
            
            for (int i = 0; i < hitCount; i++)
            {
                var hit = raycastHits[i];
                
                if (showDebugVisuals)
                {
                    Debug.DrawLine(hit.point, hit.point + hit.normal * 0.05f, Color.red, wallCheckInterval);
                }
                
                // If hit is closer to camera than the check point, we've hit a wall
                if (hit.distance < checkDistance)
                {
                    hitWall = true;
                    
                    // Check if this is the closest hit so far
                    float distanceToModel = checkDistance - hit.distance;
                    if (distanceToModel < closestDistance)
                    {
                        closestDistance = distanceToModel;
                        closestHitPoint = hit.point;
                        closestHitNormal = hit.normal;
                    }
                }
            }
            
            // Check forward from the check point
            if (showDebugVisuals)
            {
                Debug.DrawRay(worldCheckPoint, cameraForward * wallCheckDistance, Color.blue, wallCheckInterval);
            }
            
            hitCount = Physics.RaycastNonAlloc(worldCheckPoint, cameraForward, 
                raycastHits, wallCheckDistance, wallLayers);
            
            for (int i = 0; i < hitCount; i++)
            {
                var forwardHit = raycastHits[i];
                hitWall = true;
                
                // Track the closest wall
                if (forwardHit.distance < closestDistance)
                {
                    closestDistance = forwardHit.distance;
                    closestHitPoint = forwardHit.point;
                    closestHitNormal = forwardHit.normal;
                }
            }
        }
        
        // Apply avoidance if hitting a wall
        if (hitWall)
        {
            // Smoothly transition into wall avoidance
            float adjustedWallAvoidanceSpeed = isCollidingWithWall ? wallAvoidanceSpeed * 1.5f : wallAvoidanceSpeed;
            
            // Calculate push back distance - closer walls push back more
            float pushStrength = 1.0f - (closestDistance / wallCheckDistance);
            
            // Convert wall normal to local space relative to camera orientation
            Vector3 localNormal = cachedCameraTransform.InverseTransformDirection(closestHitNormal);
            
            // We want to push away from the wall (along normal) and slightly back (negative Z)
            Vector3 targetLocalOffset = localNormal * pushStrength * maxWallPushDistance;
            
            // Add a slight backward component
            targetLocalOffset.z -= pushStrength * 0.03f;
            
            // Add a slight downward component to avoid pushing up too much
            targetLocalOffset.y -= pushStrength * 0.02f;
            
            // Convert back to world space
            Vector3 targetWorldOffset = cachedCameraTransform.TransformDirection(targetLocalOffset);
            
            if (showDebugVisuals)
            {
                Debug.DrawRay(transform.position, targetWorldOffset * 10f, Color.green, wallCheckInterval);
            }
            
            // Convert to the viewmodel's local space for applying the offset
            Vector3 targetPushOffset = cachedTransform.InverseTransformDirection(targetWorldOffset);
            
            // Apply smooth wall avoidance movement
            wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, targetPushOffset, 
                                             Time.deltaTime * adjustedWallAvoidanceSpeed);
            
            // Apply rotational adjustment based on local normal
            Vector3 rotAdjust = new Vector3(
                localNormal.y * wallCollisionRotation.x,
                localNormal.x * wallCollisionRotation.y,
                localNormal.z * wallCollisionRotation.z
            );
            
            wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, 
                                                  Quaternion.Euler(rotAdjust * pushStrength), 
                                                  Time.deltaTime * adjustedWallAvoidanceSpeed);
            
            isCollidingWithWall = true;
        }
        else if (isCollidingWithWall)
        {
            // Smoothly transition back when not colliding anymore
            wallAvoidanceOffset = Vector3.Lerp(wallAvoidanceOffset, Vector3.zero, 
                                             Time.deltaTime * wallAvoidanceSpeed * 0.5f);
            
            wallAvoidanceRotation = Quaternion.Slerp(wallAvoidanceRotation, Quaternion.identity, 
                                                  Time.deltaTime * wallAvoidanceSpeed * 0.5f);
            
            // If we're almost back to normal, fully reset
            if (wallAvoidanceOffset.magnitude < 0.001f)
            {
                wallAvoidanceOffset = Vector3.zero;
                wallAvoidanceRotation = Quaternion.identity;
                isCollidingWithWall = false;
            }
        }
    }

    // Helper method to normalize euler angle differences (handle 0/360 wraparound)
    private Vector3 NormalizeEulerAngleDelta(Vector3 eulerDelta)
    {
        if (eulerDelta.x > 180f) eulerDelta.x -= 360f;
        if (eulerDelta.x < -180f) eulerDelta.x += 360f;

        if (eulerDelta.y > 180f) eulerDelta.y -= 360f;
        if (eulerDelta.y < -180f) eulerDelta.y += 360f;

        if (eulerDelta.z > 180f) eulerDelta.z -= 360f;
        if (eulerDelta.z < -180f) eulerDelta.z += 360f;

        return eulerDelta;
    }
    
    // Helper method to normalize a single angle (handle 0/360 wraparound)
    private float NormalizeAngle(float angle)
    {
        if (angle > 180f) angle -= 360f;
        if (angle < -180f) angle += 360f;
        return angle;
    }
}