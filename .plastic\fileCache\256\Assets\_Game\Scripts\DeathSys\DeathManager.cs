using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DeathManager : MonoBehaviour
{
    [<PERSON><PERSON>("References")]
    [Tooltip("Reference to the player's character controller")]
    public KinematicCharacterController.FPS.FPSCharacterController characterController;
    
    [<PERSON><PERSON><PERSON>("Reference to the player's camera")]
    public Camera playerCamera;
    
    [Toolt<PERSON>("Reference to the player's status component")]
    public PlayerStatus playerStatus;
    
    [<PERSON><PERSON>("Death Screen Settings")]
    [Tooltip("How long to stay on black screen")]
    public float blackScreenDuration = 0.5f;

    [<PERSON><PERSON>("Respawn Settings")]
    [Tooltip("How long to hold space to respawn")]
    public float holdSpaceTime = 1.5f;
    
    [Header("Debug")]
    [Tooltip("When enabled, shows debug info about wake up point selection")]
    public bool showDebugInfo = false;
    
    // Cache all wake up points in the scene
    private List<WakeUpPoint> wakeUpPoints = new List<WakeUpPoint>();
    
    // Track if death is currently being processed
    private bool isProcessingDeath = false;
    private bool isDead = false;
    
    // Track if we're ready to handle death
    private bool isInitialized = false;
    
    // Store the player's last position for distance calculations
    private Vector3 lastDeathPosition;
    
    // Store death stats
    private int deathCount = 0;

    // Black screen objects
    private GameObject blackScreen;
    private Image blackScreenImage;
    private float spaceHoldTime = 0f;
    
    // Singleton instance
    public static DeathManager Instance { get; private set; }
    
    private void Awake()
    {
        // Singleton pattern
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        // DontDestroyOnLoad(gameObject); // Removed since there won't be scene transitions
        
        CreateBlackScreen();
    }

    private void CreateBlackScreen()
    {
        GameObject canvasObj = new GameObject("DeathCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 999;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);

        GameObject imageObj = new GameObject("BlackScreenImage");
        imageObj.transform.SetParent(canvasObj.transform, false);
        blackScreenImage = imageObj.AddComponent<Image>();
        blackScreenImage.color = new Color(0, 0, 0, 0);

        RectTransform rect = imageObj.GetComponent<RectTransform>();
        rect.anchorMin = Vector2.zero;
        rect.anchorMax = Vector2.one;
        rect.sizeDelta = Vector2.zero;
        rect.anchoredPosition = Vector2.zero;

        blackScreen = canvasObj;
    }
    
    private void Start()
    {
        // Auto-find references if not set
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
        }
        
        if (characterController == null && playerStatus != null)
        {
            characterController = playerStatus.GetComponent<KinematicCharacterController.FPS.FPSCharacterController>();
        }
        
        if (playerCamera == null)
        {
            playerCamera = Camera.main;
        }
        
        // Find all wake up points in the scene
        RefreshWakeUpPoints();
        
        isInitialized = true;
    }

    private void Update()
    {
        if (isDead)
        {
            // Check for space press
            if (Input.GetKeyDown(KeyCode.Space))
            {
                StartCoroutine(DeathSequence());
            }
        }
    }
    public void RefreshWakeUpPoints()
    {
        wakeUpPoints.Clear();
        WakeUpPoint[] points = FindObjectsOfType<WakeUpPoint>();
        wakeUpPoints.AddRange(points);
        
        if (showDebugInfo)
        {
            Debug.Log($"DeathManager: Found {wakeUpPoints.Count} wake up points in the scene");
        }
    }
    
    public void HandlePlayerDeath()
    {
        if (!isInitialized || isProcessingDeath)
            return;
            
        // Track death stats
        deathCount++;
        lastDeathPosition = playerStatus.transform.position;
        isDead = true;

        // Show black screen instantly
        blackScreenImage.color = new Color(0, 0, 0, 1);
    }

    private IEnumerator DeathSequence()
    {
        if (isProcessingDeath) yield break;
        isProcessingDeath = true;
        
        // Disable player control during death
        if (characterController != null)
        {
            characterController.enabled = false;
        }
        
        // Short pause on black screen
        yield return new WaitForSeconds(blackScreenDuration);
        
        // Select wake up point and teleport player
        WakeUpPoint selectedPoint = SelectWakeUpPoint();
        if (selectedPoint != null)
        {
            // Teleport player to wake up point
            TeleportPlayer(selectedPoint);
            
            // Trigger wake up point's respawn logic
            selectedPoint.OnRespawn(playerStatus);
        }
        else
        {
            Debug.LogWarning("DeathManager: No wake up point found, respawning at last position");
        }
        
        // Restore player health and reset hits
        playerStatus.ResetHits();
        
        // Hide black screen instantly
        blackScreenImage.color = new Color(0, 0, 0, 0);
        
        // Re-enable player control
        if (characterController != null)
        {
            characterController.enabled = true;
        }
        
        isDead = false;
        isProcessingDeath = false;
        spaceHoldTime = 0f;
    }
    
    private WakeUpPoint SelectWakeUpPoint()
    {
        if (wakeUpPoints.Count == 0)
            return null;
            
        // If only one wake up point, return it
        if (wakeUpPoints.Count == 1)
            return wakeUpPoints[0];
        
        // Calculate weights for each wake up point
        float totalWeight = 0f;
        float[] weights = new float[wakeUpPoints.Count];
        
        for (int i = 0; i < wakeUpPoints.Count; i++)
        {
            weights[i] = wakeUpPoints[i].CalculateSelectionWeight(lastDeathPosition);
            totalWeight += weights[i];
            
            if (showDebugInfo)
            {
                Debug.Log($"Wake Up Point '{wakeUpPoints[i].name}' weight: {weights[i]}");
            }
        }
        
        // If no valid weights, return a random point
        if (totalWeight <= 0f)
            return wakeUpPoints[Random.Range(0, wakeUpPoints.Count)];
        
        // Select based on weighted probability
        float randomValue = Random.Range(0f, totalWeight);
        float cumulativeWeight = 0f;
        
        for (int i = 0; i < wakeUpPoints.Count; i++)
        {
            cumulativeWeight += weights[i];
            if (randomValue <= cumulativeWeight)
            {
                if (showDebugInfo)
                {
                    Debug.Log($"Selected wake up point: {wakeUpPoints[i].name}");
                }
                return wakeUpPoints[i];
            }
        }
        
        // Fallback - should never happen unless there's a calculation error
        return wakeUpPoints[wakeUpPoints.Count - 1];
    }
    
    private void TeleportPlayer(WakeUpPoint wakeUpPoint)
    {
        if (characterController == null || wakeUpPoint == null)
            return;
            
        // Set position and reset physics state
        characterController.Motor.SetPositionAndRotation(
            wakeUpPoint.transform.position, 
            wakeUpPoint.transform.rotation
        );
        
        // Reset base velocity to prevent momentum carrying over
        characterController.Motor.BaseVelocity = Vector3.zero;
        
        if (showDebugInfo)
        {
            Debug.Log($"Teleported player to wake up point: {wakeUpPoint.name}");
        }
    }
}