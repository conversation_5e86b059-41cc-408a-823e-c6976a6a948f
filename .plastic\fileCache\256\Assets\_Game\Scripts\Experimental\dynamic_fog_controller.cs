using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.HighDefinition;

public class DynamicFogController : MonoBehaviour
{
    [Head<PERSON>("Fog Settings")]
    [SerializeField] private float fogBaseOffset = 359f; // Offset above player where fog starts
    
    [Header("References")]
    [SerializeField] private Transform playerTransform;
    [SerializeField] private Volume globalVolume;
    
    [Head<PERSON>("Performance")]
    [SerializeField] private float updateInterval = 0.1f; // How often to update fog (in seconds)
    
    private Fog fogOverride;
    private float lastUpdateTime;
    
    private void Start()
    {
        InitializeFogController();
    }
    
    private void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateFogBaseHeight();
            lastUpdateTime = Time.time;
        }
    }
    
    private void InitializeFogController()
    {
        // Auto-find player if not assigned
        if (playerTransform == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTransform = player.transform;
            }
            else
            {
                Debug.LogError("DynamicFogController: No player transform assigned and no GameObject with 'Player' tag found.");
                return;
            }
        }
        
        // Auto-find global volume if not assigned
        if (globalVolume == null)
        {
            globalVolume = FindObjectOfType<Volume>();
            if (globalVolume == null)
            {
                Debug.LogError("DynamicFogController: No global volume found in scene.");
                return;
            }
        }
        
        // Get the fog override from the volume profile
        if (globalVolume.profile.TryGet<Fog>(out fogOverride))
        {
            Debug.Log("DynamicFogController: Successfully found fog override in global volume.");
            
            // Ensure fog is enabled
            if (!fogOverride.enabled.value)
            {
                Debug.LogWarning("DynamicFogController: Fog is not enabled in the volume profile.");
            }
        }
        else
        {
            Debug.LogError("DynamicFogController: No Fog override found in the global volume profile.");
        }
    }
    
    private void UpdateFogBaseHeight()
    {
        if (playerTransform == null || fogOverride == null)
        {
            return;
        }
        
        float newFogBaseHeight = playerTransform.position.y + fogBaseOffset;
        
        // Only update if the value has actually changed to avoid unnecessary overhead
        if (Mathf.Abs(fogOverride.baseHeight.value - newFogBaseHeight) > 0.1f)
        {
            fogOverride.baseHeight.Override(newFogBaseHeight);
        }
    }
    
    // Public method to adjust the offset at runtime if needed
    public void SetFogBaseOffset(float newOffset)
    {
        fogBaseOffset = newOffset;
    }
    
    // Public method to get current fog settings for debugging
    public void LogCurrentFogSettings()
    {
        if (fogOverride != null && playerTransform != null)
        {
            Debug.Log($"Player Y: {playerTransform.position.y:F1}, Fog Base Height: {fogOverride.baseHeight.value:F1}, Offset: {fogBaseOffset:F1}");
        }
    }
    
    private void OnValidate()
    {
        // Clamp the update interval to reasonable values
        updateInterval = Mathf.Clamp(updateInterval, 0.01f, 1f);
    }
}